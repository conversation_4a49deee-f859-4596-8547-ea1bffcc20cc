{"version": 3, "sources": ["navbar/navbar.js"], "names": [], "mappings": "qBASM,OAAQ,6DAIR,GAAQ,KAAU,4FAOpB,OAAI,SAAW,+EAOX,GAAI,EAAU,gFAQZ,QAAO,OAAU,KAAA,GAAA,SAAA,wFASX,MAEF,GAAU,EAAM,GAAA,iBAAU,MAAA,EAAA,UAAA,aAExB,QAAS,EAAW,SAAU,GAElC,GAAG,GAAY,QAAA,QAAW,GACxB,EAAU,EAAS,KAAQ,EAAA,WAAA,QAAA,IAAA,SACtB,SACL,EAAU,IAAA,EAAY", "file": "navbar.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.navbar', [])\n\n  .provider('$navbar', function() {\n\n    var defaults = this.defaults = {\n      activeClass: 'active',\n      routeAttr: 'data-match-route',\n      strict: false\n    };\n\n    this.$get = function() {\n      return {defaults: defaults};\n    };\n\n  })\n\n  .directive('bsNavbar', function($window, $location, $navbar) {\n\n    var defaults = $navbar.defaults;\n\n    return {\n      restrict: 'A',\n      link: function postLink(scope, element, attr, controller) {\n\n        // Directive options\n        var options = angular.copy(defaults);\n        angular.forEach(Object.keys(defaults), function(key) {\n          if(angular.isDefined(attr[key])) options[key] = attr[key];\n        });\n\n        // Watch for the $location\n        scope.$watch(function() {\n\n          return $location.path();\n\n        }, function(newValue, oldValue) {\n\n          var liElements = element[0].querySelectorAll('li[' + options.routeAttr + ']');\n\n          angular.forEach(liElements, function(li) {\n\n            var liElement = angular.element(li);\n            var pattern = liElement.attr(options.routeAttr).replace('/', '\\\\/');\n            if(options.strict) {\n              pattern = '^' + pattern + '$';\n            }\n            var regexp = new RegExp(pattern, ['i']);\n\n            if(regexp.test(newValue)) {\n              liElement.addClass(options.activeClass);\n            } else {\n              liElement.removeClass(options.activeClass);\n            }\n\n          });\n\n        });\n\n      }\n\n    };\n\n  });\n"], "sourceRoot": "/source/"}