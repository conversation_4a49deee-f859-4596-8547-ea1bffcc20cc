4.0.5
  - Update `jscs` so tests pass

4.0.4
  - Style/indentation/whitespace cleanups.
  - README tweaks

4.0.3
  - Fix keywords (#268)
  - add some Date tests
  - Note in README that the es5-sham requires the es5-shim (https://github.com/es-shims/es5-shim/issues/256#issuecomment-52875710)

4.0.2
  - Start including version numbers in minified files (#267)

4.0.1
  - Fix legacy arguments object detection in Object.keys (#260)

4.0.0
  - No longer shim the ES5-spec behavior of splice when `deleteCount` is omitted - since no engines implement it, and ES6 changes it. (#255)
  - Use Object.defineProperty where available, so that polyfills are non-enumerable when possible (#250)
  - lots of internal refactoring
  - Fixed a bug referencing String#indexOf and String#lastIndexOf before polyfilling it (#253, #254)

3.4.0
  - Removed nonstandard SpiderMonkey extension to Array#splice - when `deleteCount` is omitted, it's now treated as 0. (#192, #239)
  - Fix Object.keys with Arguments objects in Safari 5.0
  - Now shimming String#split in Opera 10.6
  - Avoid using "toString" as a variable name, since that breaks Opera
  - Internal implementation and test cleanups

3.3.2
 - Remove an internal "bind" call, which should make the shim a bit faster
 - Fix a bug with object boxing in Array#reduceRight that was failing a test in IE 6

3.3.1
 - Fixing an Array#splice bug in IE 6/7
 - cleaning up Array#splice tests

3.3.0
 - Fix Array#reduceRight in node 0.6 and older browsers (#238)

3.2.0
 - Fix es5-sham UMD definition to work properly with AMD (#237)
 - Ensure that Array methods do not autobox context in strict mode (#233)

3.1.1
 - Update minified files (#231)

3.1.0
 - Fix String#replace in Firefox up through 29 (#228)

3.0.2
 - Fix `Function#bind` in IE 7 and 8 (#224, #225, #226)

3.0.1
 - Version bump to ensure npm has newest minified assets

3.0.0
 - es5-sham: fix `Object.getPrototypeOf` and `Object.getOwnPropertyDescriptor` for Opera Mini
 - Better override noncompliant native ES5 methods: `Array#forEach`, `Array#map`, `Array#filter`, `Array#every`, `Array#some`, `Array#reduce`, `Date.parse`, `String#trim`
 - Added spec-compliant shim for `parseInt`
 - Ensure `Object.keys` handles more edge cases with `arguments` objects and boxed primitives
 - Improve minification of builds

2.3.0
 - parseInt is now properly shimmed in ES3 browsers to default the radix
 - update URLs to point to the new organization

2.2.0
 - Function.prototype.bind shim now reports correct length on a bound function
 - fix node 0.6.x v8 bug in Array#forEach
 - test improvements

2.1.0
 - Object.create fixes
 - tweaks to the Object.defineProperties shim

2.0.0
 - Separate reliable shims from dubious shims (shams).

1.2.10
 - Group-effort Style Cleanup
 - Took a stab at fixing Object.defineProperty on IE8 without
   bad side-effects. (@hax)
 - Object.isExtensible no longer fakes it. (@xavierm)
 - Date.prototype.toISOString no longer deals with partial
   ISO dates, per spec (@kitcambridge)
 - More (mostly from @bryanforbes)

1.2.9
 - Corrections to toISOString by @kitcambridge
 - Fixed three bugs in array methods revealed by Jasmine tests.
 - Cleaned up Function.prototype.bind with more fixes and tests from
   @bryanforbes.

1.2.8
 - Actually fixed problems with Function.prototype.bind, and regressions
   from 1.2.7 (@bryanforbes, @jdalton #36)

1.2.7 - REGRESSED
 - Fixed problems with Function.prototype.bind when called as a constructor.
   (@jdalton #36)

1.2.6
 - Revised Date.parse to match ES 5.1 (kitcambridge)

1.2.5
 - Fixed a bug for padding it Date..toISOString (tadfisher issue #33)

1.2.4
 - Fixed a descriptor bug in Object.defineProperty (raynos)

1.2.3
 - Cleaned up RequireJS and <script> boilerplate

1.2.2
 - Changed reduce to follow the letter of the spec with regard to having and
   owning properties.
 - Fixed a bug where RegExps pass as Functions in some engines in reduce.

1.2.1
 - Adding few fixes to make jshint happy.
 - Fix for issue #12, function expressions can cause scoping issues in IE.
 - NPM will minify on install or when `npm run-script install` is executed.
 - Adding .gitignore to avoid publishing dev dependencies.

1.2.0
 - Making script loadable as AMD module.
 - Adding `indexOf` to the list of safe shims.

1.1.0
 - Added support for accessor properties where possible (which is all browsers
   except IE).
 - Stop exposing bound function's (that are returned by
   `Function.prototype.bind`) internal properties (`bound, boundTo, boundArgs`)
   as in some cases (when using facade objects for example) capabilities of the
   enclosed functions will be leaked.
 - `Object.create` now explicitly sets `__proto__` property to guarantee
   correct behavior of `Object.getPrototypeOf`'s on all objects created using
   `Object.create`.
 - Switched to `===` from `==` where possible as it's slightly faster on older
   browsers that are target of this lib.
 - Added names to all anonymous functions to have a better stack traces.

1.0.0
 - fixed Date.toISODate, using UTC accessors, as in
   http://code.google.com/p/v8/source/browse/trunk/src/date.js?r=6120#986
   (arian)

0.0.4
 - Revised Object.getPrototypeOf to work in more cases
   in response to http://ejohn.org/blog/objectgetprototypeof/
   [issue #2] (fschaefer)

0.0.3
 - Fixed typos in Object.keys (samsonjs)

0.0.2
   Per kangax's recommendations:
 - faster Object.create(null)
 - fixed a function-scope function declaration statement in Object.create

0.0.1
 - fixed Object.create(null), in so far as that's possible
 - reworked Rhino Object.freeze(Function) bug detector and patcher

0.0.0
 - forked from narwhal-lib

