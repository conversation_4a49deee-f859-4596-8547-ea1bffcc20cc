{"version": 3, "sources": ["select/select.js"], "names": [], "mappings": "qBASM,OAAA,yBAAa,yBAAA,iDAEb,UAAU,cAEV,GAAW,KAAA,UACX,UAAU,UACV,YAAM,SACN,YAAO,UACP,UAAU,cACV,SAAA,yBACA,QAAM,QACN,WAAW,EACX,UAAA,EACA,MAAA,EACA,MAAA,EACA,UAAA,EACA,gBAAe,EACf,MAAA,4FAGF,QAAK,sBAEH,UAAI,EACJ,cAAe,WACf,cAAe,0HASb,GAAmB,EAAS,EAAA,YAK5B,EAAM,QAAc,UAAQ,EAAA,EAE5B,GAAM,EAAA,EAAiB,EACvB,IAAA,GAAM,EAAW,qBAGjB,EAAM,aAAY,IAChB,YAAM,EAAa,WACjB,oBAAiB,EAAA,gBAAA,EAAA,wFAKnB,UAAM,SAAa,KACjB,aAAe,8BAKjB,QAAO,SAAQ,oEAOjB,MAAM,GAAA,gBAGA,UAAM,SAAQ,kDAKpB,IAAM,GAAA,GAAA,EAAA,EAAc,EAAA,SAAY,OAAA,IACzB,EAAQ,UAAO,IAClB,EAAI,QAAM,iFAQd,EAAQ,QAAS,MAOb,OAAM,SAAa,KACnB,SAAQ,IACL,mDAIL,GAAO,yHAGT,EAAQ,MAAS,EAAS,aAAO,QAE/B,EAAM,aAAO,EAER,EAAQ,yCAIJ,EAAA,SAAA,GAAA,QACL,OAAA,yBAEA,EAAQ,wHAWP,MAAA,EAAQ,YAAoB,UAAQ,EAAW,EAAA,MAKhD,mBAAqB,4CAGvB,EAAM,eADE,UAAM,QAAgB,QAAM,EAAS,aACxB,EAAQ,YAAgB,IAAA,SAAA,2BAI5B,EAAW,UAAA,EAAA,aAErB,EAAM,cAAS,EAAA,SAAA,kEAM1B,MAAA,GAAQ,WAAY,EAIhB,EAAO,SAAM,QAAiB,EAAA,WAAA,QAAA,EAAA,UAH7B,EAAQ,SAAU,gCAOvB,MAAA,GAAQ,SAC6B,KAA3B,EAAM,aAAS,QAAY,GAE3B,EAAG,eAAM,KAIjB,UAAO,SAAA,qCAGT,IAAA,EAAQ,EAAA,+BAGN,KAAI,EAAJ,gBAIE,aAAS,SAAe,GAK1B,0CAAA,EAAK,CACL,GAAI,GAAA,QAAA,QAAA,EAAA,OACJ,GAAI,eAAA,aAIF,WAAe,SAAO,mFAMhB,WAA0B,KAAlB,EAAY,SAA2B,IAArB,EAAe,SACjD,MAAM,GAAA,OAAA,EAAA,gEAKY,KAAhB,EAAQ,SAAQ,EAAA,aAAA,EAAA,SAAA,OAAA,EAAA,EAAA,eACZ,QAAO,YAAW,EAAA,gBAAA,EAAA,aAAA,GACxB,EAAA,2BAMA,KAAS,eAEP,EAAG,YACD,SAAW,SAAW,gCAKxB,EAAQ,SAAQ,GAAA,EAAA,aAAA,YAAA,EAAA,cACpB,EAAe,UACb,EAAQ,GAAA,UAAa,EAAU,aAE7B,GAAA,0CAKJ,EAAO,SAAA,IAAA,EAAA,aAAA,YAAA,EAAA,wDAIT,GAAA,yGA1LE,EAAI,eAAyB,GAAI,UAAU,QAiMhD,GAAU,SAAA,kBAOD,YAAS,UAAgB,SAAS,KAAM,UAAY,gBAAA,SAAA,EAAA,EAAA,EAAA,EAAA,oCAKnD,kDAKH,IAAI,MAAU,EAAA,YAAA,EAAA,wBACd,SAAY,YAAW,YAAA,QAAA,UAAA,WAAA,OAAA,YAAA,WAAA,cAAA,WAAA,iBAAA,YAAA,gBAAA,UAAA,WAAA,gBAAA,YAAA,MAAA,SAAA,GACvB,QAAU,UAAQ,EAAQ,MAAA,EAAA,GAAA,EAAA,8CAK5B,GAAI,GAAA,uGAGJ,EAAI,MAAS,GAIb,GAAA,GAAa,EAAgB,EAAS,WAGnC,EAAK,EAAS,EAAQ,EAAA,6CAItB,OAAA,EAAA,2BAGH,KAAM,SAAO,eAEX,EAAO,cAEN,GAGH,EAAA,OAAW,EAAA,QAAU,WAEnB,EAAI,qBACJ,EAAW,iBAIN,QAAO,cAER,GAAA,IACK,UAAA,QAAA,QAAA,EAAA,gBACL,EAAoB,YAAK,IAAA,SAAA,2BAEtB,QAAA,UAAA,GAAA,EAAA,OAAA,SAAA,GAAA,OAAA,IACL,OAAQ,QAAO,aACf,EAAW,QAAQ,EAAU,WAAS,EAAc,0EAMtD,EAAA,EAAW,UAAW,EAAe,aACnC,EAAQ,QAAS,UAAM,GAAW,EAAA,OAAA,SAAA,GAAA,OAAA,4CAKtC,EAAU,WACR,EAAI,SAAe,SAAA,GACnB,OAAU,GAAA,IAAA,EAAA,oDAOf,EAAA", "file": "select.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.select', ['mgcrea.ngStrap.tooltip', 'mgcrea.ngStrap.helpers.parseOptions'])\n\n  .provider('$select', function() {\n\n    var defaults = this.defaults = {\n      animation: 'am-fade',\n      prefixClass: 'select',\n      prefixEvent: '$select',\n      placement: 'bottom-left',\n      template: 'select/select.tpl.html',\n      trigger: 'focus',\n      container: false,\n      keyboard: true,\n      html: false,\n      delay: 0,\n      multiple: false,\n      allNoneButtons: false,\n      sort: true,\n      caretHtml: '&nbsp;<span class=\"caret\"></span>',\n      placeholder: 'Choose among the following...',\n      allText: 'All',\n      noneText: 'None',\n      maxLength: 3,\n      maxLengthHtml: 'selected',\n      iconCheckmark: 'glyphicon glyphicon-ok'\n    };\n\n    this.$get = function($window, $document, $rootScope, $tooltip, $timeout) {\n\n      var bodyEl = angular.element($window.document.body);\n      var isNative = /(ip(a|o)d|iphone|android)/ig.test($window.navigator.userAgent);\n      var isTouch = ('createTouch' in $window.document) && isNative;\n\n      function SelectFactory(element, controller, config) {\n\n        var $select = {};\n\n        // Common vars\n        var options = angular.extend({}, defaults, config);\n\n        $select = $tooltip(element, options);\n        var scope = $select.$scope;\n\n        scope.$matches = [];\n        scope.$activeIndex = 0;\n        scope.$isMultiple = options.multiple;\n        scope.$showAllNoneButtons = options.allNoneButtons && options.multiple;\n        scope.$iconCheckmark = options.iconCheckmark;\n        scope.$allText = options.allText;\n        scope.$noneText = options.noneText;\n\n        scope.$activate = function(index) {\n          scope.$$postDigest(function() {\n            $select.activate(index);\n          });\n        };\n\n        scope.$select = function(index, evt) {\n          scope.$$postDigest(function() {\n            $select.select(index);\n          });\n        };\n\n        scope.$isVisible = function() {\n          return $select.$isVisible();\n        };\n\n        scope.$isActive = function(index) {\n          return $select.$isActive(index);\n        };\n\n        scope.$selectAll = function () {\n          for (var i = 0; i < scope.$matches.length; i++) {\n            if (!scope.$isActive(i)) {\n              scope.$select(i);\n            }\n          }\n        };\n\n        scope.$selectNone = function () {\n          for (var i = 0; i < scope.$matches.length; i++) {\n            if (scope.$isActive(i)) {\n              scope.$select(i);\n            }\n          }\n        };\n\n        // Public methods\n\n        $select.update = function(matches) {\n          scope.$matches = matches;\n          $select.$updateActiveIndex();\n        };\n\n        $select.activate = function(index) {\n          if(options.multiple) {\n            scope.$activeIndex.sort();\n            $select.$isActive(index) ? scope.$activeIndex.splice(scope.$activeIndex.indexOf(index), 1) : scope.$activeIndex.push(index);\n            if(options.sort) scope.$activeIndex.sort();\n          } else {\n            scope.$activeIndex = index;\n          }\n          return scope.$activeIndex;\n        };\n\n        $select.select = function(index) {\n          var value = scope.$matches[index].value;\n          scope.$apply(function() {\n            $select.activate(index);\n            if(options.multiple) {\n              controller.$setViewValue(scope.$activeIndex.map(function(index) {\n                return scope.$matches[index].value;\n              }));\n            } else {\n              controller.$setViewValue(value);\n              // Hide if single select\n              $select.hide();\n            }\n          });\n          // Emit event\n          scope.$emit(options.prefixEvent + '.select', value, index, $select);\n        };\n\n        // Protected methods\n\n        $select.$updateActiveIndex = function() {\n          if(controller.$modelValue && scope.$matches.length) {\n            if(options.multiple && angular.isArray(controller.$modelValue)) {\n              scope.$activeIndex = controller.$modelValue.map(function(value) {\n                return $select.$getIndex(value);\n              });\n            } else {\n              scope.$activeIndex = $select.$getIndex(controller.$modelValue);\n            }\n          } else if(scope.$activeIndex >= scope.$matches.length) {\n            scope.$activeIndex = options.multiple ? [] : 0;\n          }\n        };\n\n        $select.$isVisible = function() {\n          if(!options.minLength || !controller) {\n            return scope.$matches.length;\n          }\n          // minLength support\n          return scope.$matches.length && controller.$viewValue.length >= options.minLength;\n        };\n\n        $select.$isActive = function(index) {\n          if(options.multiple) {\n            return scope.$activeIndex.indexOf(index) !== -1;\n          } else {\n            return scope.$activeIndex === index;\n          }\n        };\n\n        $select.$getIndex = function(value) {\n          var l = scope.$matches.length, i = l;\n          if(!l) return;\n          for(i = l; i--;) {\n            if(scope.$matches[i].value === value) break;\n          }\n          if(i < 0) return;\n          return i;\n        };\n\n        $select.$onMouseDown = function(evt) {\n          // Prevent blur on mousedown on .dropdown-menu\n          evt.preventDefault();\n          evt.stopPropagation();\n          // Emulate click for mobile devices\n          if(isTouch) {\n            var targetEl = angular.element(evt.target);\n            targetEl.triggerHandler('click');\n          }\n        };\n\n        $select.$onKeyDown = function(evt) {\n          if (!/(9|13|38|40)/.test(evt.keyCode)) return;\n          evt.preventDefault();\n          evt.stopPropagation();\n\n          // Select with enter\n          if(!options.multiple && (evt.keyCode === 13 || evt.keyCode === 9)) {\n            return $select.select(scope.$activeIndex);\n          }\n\n          // Navigate with keyboard\n          if(evt.keyCode === 38 && scope.$activeIndex > 0) scope.$activeIndex--;\n          else if(evt.keyCode === 40 && scope.$activeIndex < scope.$matches.length - 1) scope.$activeIndex++;\n          else if(angular.isUndefined(scope.$activeIndex)) scope.$activeIndex = 0;\n          scope.$digest();\n        };\n\n        // Overrides\n\n        var _show = $select.show;\n        $select.show = function() {\n          _show();\n          if(options.multiple) {\n            $select.$element.addClass('select-multiple');\n          }\n          // use timeout to hookup the events to prevent\n          // event bubbling from being processed imediately.\n          $timeout(function() {\n            $select.$element.on(isTouch ? 'touchstart' : 'mousedown', $select.$onMouseDown);\n            if(options.keyboard) {\n              element.on('keydown', $select.$onKeyDown);\n            }\n          }, 0, false);\n        };\n\n        var _hide = $select.hide;\n        $select.hide = function() {\n          $select.$element.off(isTouch ? 'touchstart' : 'mousedown', $select.$onMouseDown);\n          if(options.keyboard) {\n            element.off('keydown', $select.$onKeyDown);\n          }\n          _hide(true);\n        };\n\n        return $select;\n\n      }\n\n      SelectFactory.defaults = defaults;\n      return SelectFactory;\n\n    };\n\n  })\n\n  .directive('bsSelect', function($window, $parse, $q, $select, $parseOptions) {\n\n    var defaults = $select.defaults;\n\n    return {\n      restrict: 'EAC',\n      require: 'ngModel',\n      link: function postLink(scope, element, attr, controller) {\n\n        // Directive options\n        var options = {scope: scope, placeholder: defaults.placeholder};\n        angular.forEach(['placement', 'container', 'delay', 'trigger', 'keyboard', 'html', 'animation', 'template', 'placeholder', 'multiple', 'allNoneButtons', 'maxLength', 'maxLengthHtml', 'allText', 'noneText', 'iconCheckmark', 'autoClose', 'id'], function(key) {\n          if(angular.isDefined(attr[key])) options[key] = attr[key];\n        });\n\n        // Add support for select markup\n        if(element[0].nodeName.toLowerCase() === 'select') {\n          var inputEl = element;\n          inputEl.css('display', 'none');\n          element = angular.element('<button type=\"button\" class=\"btn btn-default\"></button>');\n          inputEl.after(element);\n        }\n\n        // Build proper ngOptions\n        var parsedOptions = $parseOptions(attr.ngOptions);\n\n        // Initialize select\n        var select = $select(element, controller, options);\n\n        // Watch ngOptions values before filtering for changes\n        var watchedOptions = parsedOptions.$match[7].replace(/\\|.+/, '').trim();\n        scope.$watch(watchedOptions, function(newValue, oldValue) {\n          // console.warn('scope.$watch(%s)', watchedOptions, newValue, oldValue);\n          parsedOptions.valuesFn(scope, controller)\n          .then(function(values) {\n            select.update(values);\n            controller.$render();\n          });\n        }, true);\n\n        // Watch model for changes\n        scope.$watch(attr.ngModel, function(newValue, oldValue) {\n          // console.warn('scope.$watch(%s)', attr.ngModel, newValue, oldValue);\n          select.$updateActiveIndex();\n          controller.$render();\n        }, true);\n\n        // Model rendering in view\n        controller.$render = function () {\n          // console.warn('$render', element.attr('ng-model'), 'controller.$modelValue', typeof controller.$modelValue, controller.$modelValue, 'controller.$viewValue', typeof controller.$viewValue, controller.$viewValue);\n          var selected, index;\n          if(options.multiple && angular.isArray(controller.$modelValue)) {\n            selected = controller.$modelValue.map(function(value) {\n              index = select.$getIndex(value);\n              return angular.isDefined(index) ? select.$scope.$matches[index].label : false;\n            }).filter(angular.isDefined);\n            if(selected.length > (options.maxLength || defaults.maxLength)) {\n              selected = selected.length + ' ' + (options.maxLengthHtml || defaults.maxLengthHtml);\n            } else {\n              selected = selected.join(', ');\n            }\n          } else {\n            index = select.$getIndex(controller.$modelValue);\n            selected = angular.isDefined(index) ? select.$scope.$matches[index].label : false;\n          }\n          element.html((selected ? selected : options.placeholder) + defaults.caretHtml);\n        };\n\n        if(options.multiple){\n          controller.$isEmpty = function(value){\n            return !value || value.length === 0;\n          };\n        }\n\n        // Garbage collection\n        scope.$on('$destroy', function() {\n          if (select) select.destroy();\n          options = null;\n          select = null;\n        });\n\n      }\n    };\n\n  });\n"], "sourceRoot": "/source/"}