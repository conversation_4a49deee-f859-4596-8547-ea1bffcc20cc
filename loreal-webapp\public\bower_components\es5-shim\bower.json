{"name": "es5-shim", "version": "4.0.5", "main": "es5-shim.js", "repository": {"type": "git", "url": "git://github.com/es-shims/es5-shim"}, "homepage": "https://github.com/es-shims/es5-shim", "authors": ["<PERSON> <<EMAIL>> (http://github.com/kriskowal/)", "<PERSON> <<EMAIL>> (http://samhuri.net/)", "<PERSON><PERSON><PERSON> <<EMAIL>> (http://github.com/fschaefer)", "<PERSON><PERSON><PERSON> <<EMAIL>> (http://jeditoolkit.com)", "<PERSON> <<EMAIL>> (http://kitcambridge.github.com)", "<PERSON> <<EMAIL>> (https://github.com/ljharb/)"], "description": "ECMAScript 5 compatibility shims for legacy JavaScript engines", "keywords": ["shim", "es5", "es5 shim", "javascript", "ecmascript", "polyfill"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "tests"]}