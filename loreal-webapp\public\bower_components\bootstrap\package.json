{"name": "bootstrap", "description": "The most popular front-end framework for developing responsive, mobile first projects on the web.", "version": "3.2.0", "keywords": ["css", "less", "mobile-first", "responsive", "front-end", "framework", "web"], "homepage": "http://getbootstrap.com", "author": "Twitter, Inc.", "scripts": {"test": "grunt test"}, "style": "dist/css/bootstrap.css", "less": "less/bootstrap.less", "repository": {"type": "git", "url": "https://github.com/twbs/bootstrap.git"}, "bugs": {"url": "https://github.com/twbs/bootstrap/issues"}, "license": {"type": "MIT", "url": "https://github.com/twbs/bootstrap/blob/master/LICENSE"}, "devDependencies": {"btoa": "~1.1.2", "glob": "~4.0.2", "grunt": "~0.4.5", "grunt-autoprefixer": "~0.7.6", "grunt-banner": "~0.2.3", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.4.0", "grunt-contrib-connect": "~0.8.0", "grunt-contrib-copy": "~0.5.0", "grunt-contrib-csslint": "~0.2.0", "grunt-contrib-cssmin": "~0.10.0", "grunt-contrib-jade": "~0.12.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-less": "~0.11.3", "grunt-contrib-qunit": "~0.5.1", "grunt-contrib-uglify": "~0.5.0", "grunt-contrib-watch": "~0.6.1", "grunt-csscomb": "~2.0.1", "grunt-exec": "~0.4.5", "grunt-html-validation": "~0.1.18", "grunt-jekyll": "~0.4.2", "grunt-jscs-checker": "~0.6.0", "grunt-saucelabs": "~8.1.0", "grunt-sed": "~0.1.1", "load-grunt-tasks": "~0.6.0", "markdown": "~0.5.0", "npm-shrinkwrap": "~3.1.6", "time-grunt": "~0.3.2"}, "engines": {"node": "~0.10.1"}, "jspm": {"main": "js/bootstrap", "directories": {"example": "examples", "lib": "dist"}, "shim": {"js/bootstrap": {"imports": "j<PERSON>y", "exports": "$"}}, "buildConfig": {"uglify": true}}}