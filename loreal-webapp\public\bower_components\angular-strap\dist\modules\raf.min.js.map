{"version": 3, "sources": ["helpers/raf.js"], "names": [], "mappings": "8GAW6B,WAAQ,SAAA,EAAA,SAER,EAAQ,sDAEd,EAAA,yBAEnB,EAAa,EAAA,sBACF,EAAsB,4BACb,EAAA,yBACK,EAAA,wCAGzB,EAAA,WACM,GACJ,GAAA,GAAO,EAAW,SAChB,mBAIN,SAAI,sBAEJ,OAAO", "file": "raf.min.js", "sourcesContent": ["'use strict';\n\n(angular.version.minor < 3 && angular.version.dot < 14) && angular.module('ng')\n\n.factory('$$rAF', function($window, $timeout) {\n\n  var requestAnimationFrame = $window.requestAnimationFrame ||\n                              $window.webkitRequestAnimationFrame ||\n                              $window.mozRequestAnimationFrame;\n\n  var cancelAnimationFrame = $window.cancelAnimationFrame ||\n                             $window.webkitCancelAnimationFrame ||\n                             $window.mozCancelAnimationFrame ||\n                             $window.webkitCancelRequestAnimationFrame;\n\n  var rafSupported = !!requestAnimationFrame;\n  var raf = rafSupported ?\n    function(fn) {\n      var id = requestAnimationFrame(fn);\n      return function() {\n        cancelAnimationFrame(id);\n      };\n    } :\n    function(fn) {\n      var timer = $timeout(fn, 16.66, false); // 1000 / 60 = 16.666\n      return function() {\n        $timeout.cancel(timer);\n      };\n    };\n\n  raf.supported = rafSupported;\n\n  return raf;\n\n});\n\n// .factory('$$animateReflow', function($$rAF, $document) {\n\n//   var bodyEl = $document[0].body;\n\n//   return function(fn) {\n//     //the returned function acts as the cancellation function\n//     return $$rAF(function() {\n//       //the line below will force the browser to perform a repaint\n//       //so that all the animated elements within the animation frame\n//       //will be properly updated and drawn on screen. This is\n//       //required to perform multi-class CSS based animations with\n//       //Firefox. DO NOT REMOVE THIS LINE.\n//       var a = bodyEl.offsetWidth + 1;\n//       fn();\n//     });\n//   };\n\n// });\n"], "sourceRoot": "/source/"}