{"version": 3, "sources": ["button/button.js"], "names": [], "mappings": "iEAWS,UAAO,+FAQZ,OAAO,SAAA,gBAKH,kBAAmB,2BAGb,YACJ,kBACA,SAAa,EAAiB,mQAelC,cAAS,UAAA,QAAA,SAAA,EAAA,oBAGP,EAAc,qCAGV,YACA,iCAGJ,GAAG,GAAA,EAGC,EAA+B,UAA/B,EAAa,GAAQ,SACtB,EAAA,EAAyB,EAAK,SAAa,+FAM9C,IAAG,GAAA,QAAiB,UAAA,EAAA,YAAA,EAAA,YAAA,CAClB,GAAoB,KAAK,EAAA,0CAKzB,GAAqC,iBAAd,IAA0B,iBAAA,SAEvC,SAAQ,KAAO,SAAA,4GAWzB,EAAI,uEAQN,GAAQ,WACN,IAAa,EAAA,GAAY,QAAA,uCAMrB,KAAA,EAAW,YAAA,2GAkBjB,eAAmB,2BAGjB,YACA,+QAeJ,WAAS,UAAA,QAAA,SAAA,EAAA,oBAGP,EAAc,qCAGV,YACA,mEAOF,EAAe,EAAQ,EAAO,SAAW,EAEvC,EAAG,EAAoB,KAAU,EAAA,OAAA,EAAA,MAAA,EAAA,OAAA,EAAA,gEAMrC,GAAQ,WACN,IAAa,EAAA,GAAY,QAAA", "file": "button.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.button', [])\n\n  .provider('$button', function() {\n\n    var defaults = this.defaults = {\n      activeClass:'active',\n      toggleEvent:'click'\n    };\n\n    this.$get = function() {\n      return {defaults: defaults};\n    };\n\n  })\n\n  .directive('bsCheckboxGroup', function() {\n\n    return {\n      restrict: 'A',\n      require: 'ngModel',\n      compile: function postLink(element, attr) {\n        element.attr('data-toggle', 'buttons');\n        element.removeAttr('ng-model');\n        var children = element[0].querySelectorAll('input[type=\"checkbox\"]');\n        angular.forEach(children, function(child) {\n          var childEl = angular.element(child);\n          childEl.attr('bs-checkbox', '');\n          childEl.attr('ng-model', attr.ngModel + '.' + childEl.attr('value'));\n        });\n      }\n\n    };\n\n  })\n\n  .directive('bsCheckbox', function($button, $$rAF) {\n\n    var defaults = $button.defaults;\n    var constantValueRegExp = /^(true|false|\\d+)$/;\n\n    return {\n      restrict: 'A',\n      require: 'ngModel',\n      link: function postLink(scope, element, attr, controller) {\n\n        var options = defaults;\n\n        // Support label > input[type=\"checkbox\"]\n        var isInput = element[0].nodeName === 'INPUT';\n        var activeElement = isInput ? element.parent() : element;\n\n        var trueValue = angular.isDefined(attr.trueValue) ? attr.trueValue : true;\n        if(constantValueRegExp.test(attr.trueValue)) {\n          trueValue = scope.$eval(attr.trueValue);\n        }\n        var falseValue = angular.isDefined(attr.falseValue) ? attr.falseValue : false;\n        if(constantValueRegExp.test(attr.falseValue)) {\n          falseValue = scope.$eval(attr.falseValue);\n        }\n\n        // Parse exotic values\n        var hasExoticValues = typeof trueValue !== 'boolean' || typeof falseValue !== 'boolean';\n        if(hasExoticValues) {\n          controller.$parsers.push(function(viewValue) {\n            // console.warn('$parser', element.attr('ng-model'), 'viewValue', viewValue);\n            return viewValue ? trueValue : falseValue;\n          });\n          // modelValue -> $formatters -> viewValue\n          controller.$formatters.push(function(modelValue) {\n             // console.warn('$formatter(\"%s\"): modelValue=%o (%o)', element.attr('ng-model'), modelValue, typeof modelValue);\n             return angular.equals(modelValue, trueValue);\n          });\n          // Fix rendering for exotic values\n          scope.$watch(attr.ngModel, function(newValue, oldValue) {\n            controller.$render();\n          });\n        }\n\n        // model -> view\n        controller.$render = function () {\n          // console.warn('$render', element.attr('ng-model'), 'controller.$modelValue', typeof controller.$modelValue, controller.$modelValue, 'controller.$viewValue', typeof controller.$viewValue, controller.$viewValue);\n          var isActive = angular.equals(controller.$modelValue, trueValue);\n          $$rAF(function() {\n            if(isInput) element[0].checked = isActive;\n            activeElement.toggleClass(options.activeClass, isActive);\n          });\n        };\n\n        // view -> model\n        element.bind(options.toggleEvent, function() {\n          scope.$apply(function () {\n            // console.warn('!click', element.attr('ng-model'), 'controller.$viewValue', typeof controller.$viewValue, controller.$viewValue, 'controller.$modelValue', typeof controller.$modelValue, controller.$modelValue);\n            if(!isInput) {\n              controller.$setViewValue(!activeElement.hasClass('active'));\n            }\n            if(!hasExoticValues) {\n              controller.$render();\n            }\n          });\n        });\n\n      }\n\n    };\n\n  })\n\n  .directive('bsRadioGroup', function() {\n\n    return {\n      restrict: 'A',\n      require: 'ngModel',\n      compile: function postLink(element, attr) {\n        element.attr('data-toggle', 'buttons');\n        element.removeAttr('ng-model');\n        var children = element[0].querySelectorAll('input[type=\"radio\"]');\n        angular.forEach(children, function(child) {\n          angular.element(child).attr('bs-radio', '');\n          angular.element(child).attr('ng-model', attr.ngModel);\n        });\n      }\n\n    };\n\n  })\n\n  .directive('bsRadio', function($button, $$rAF) {\n\n    var defaults = $button.defaults;\n    var constantValueRegExp = /^(true|false|\\d+)$/;\n\n    return {\n      restrict: 'A',\n      require: 'ngModel',\n      link: function postLink(scope, element, attr, controller) {\n\n        var options = defaults;\n\n        // Support `label > input[type=\"radio\"]` markup\n        var isInput = element[0].nodeName === 'INPUT';\n        var activeElement = isInput ? element.parent() : element;\n\n        var value = constantValueRegExp.test(attr.value) ? scope.$eval(attr.value) : attr.value;\n\n        // model -> view\n        controller.$render = function () {\n          // console.warn('$render', element.attr('value'), 'controller.$modelValue', typeof controller.$modelValue, controller.$modelValue, 'controller.$viewValue', typeof controller.$viewValue, controller.$viewValue);\n          var isActive = angular.equals(controller.$modelValue, value);\n          $$rAF(function() {\n            if(isInput) element[0].checked = isActive;\n            activeElement.toggleClass(options.activeClass, isActive);\n          });\n        };\n\n        // view -> model\n        element.bind(options.toggleEvent, function() {\n          scope.$apply(function () {\n            // console.warn('!click', element.attr('value'), 'controller.$viewValue', typeof controller.$viewValue, controller.$viewValue, 'controller.$modelValue', typeof controller.$modelValue, controller.$modelValue);\n            controller.$setViewValue(value);\n            controller.$render();\n          });\n        });\n\n      }\n\n    };\n\n  });\n"], "sourceRoot": "/source/"}