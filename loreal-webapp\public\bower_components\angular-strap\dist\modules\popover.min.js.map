{"version": 3, "sources": ["popover/popover.js"], "names": [], "mappings": "qBASM,OAAA,0BAAW,oCAEX,WAAW,cAEX,GAAA,KAAiB,UACjB,UAAS,UACT,YAAU,GACV,WAAM,EACN,QAAO,EACP,UAAS,QACT,SAAO,2BACP,iBAAW,8BAGb,MAAK,WAEH,QAAS,8BAKP,MAAI,WAAW,SAAS,gDAOxB,EAAO,EAAA,EAAA,sBAIT,EAAO,OAAA,QAAA,EAAA,kCAaD,aAAS,UAAgB,OAAS,WAAM,SAAA,EAAA,EAAA,+DAKvC,uCAKH,IAAa,MAAK,WAChB,SAAa,WAAK,kBAAY,YAAA,YAAA,SAAA,QAAA,UAAA,WAAA,OAAA,YAAA,cAAA,YAAA,MAAA,SAAA,GAC9B,QAAQ,UAAU,EAAA,MAAa,EAAA,GAAA,EAAA,6GAOnC,QAAK,UAAa,IAAa,EAAyB,WACnD,GAAQ,EAAS,0BAMlB,WAAW,EAAQ,OAAA,EAAA,UAAA,SAAA,EAAA,uBAEpB,QAAA,OAAA,EAAA,eAID,QAAI,UAAY,IAAQ,EAAqB,WAC7C,GAAW,EAAS,yBAKtB,EAAI,QAAU,EAAA,OAAS,EAAS,OAAA,SAAA,uFAGhC,KAAU,EAAY,EAAA,OAAW,EAAA,kEASpC,EAAA", "file": "popover.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.popover', ['mgcrea.ngStrap.tooltip'])\n\n  .provider('$popover', function() {\n\n    var defaults = this.defaults = {\n      animation: 'am-fade',\n      customClass: '',\n      container: false,\n      target: false,\n      placement: 'right',\n      template: 'popover/popover.tpl.html',\n      contentTemplate: false,\n      trigger: 'click',\n      keyboard: true,\n      html: false,\n      title: '',\n      content: '',\n      delay: 0,\n      autoClose: false\n    };\n\n    this.$get = function($tooltip) {\n\n      function PopoverFactory(element, config) {\n\n        // Common vars\n        var options = angular.extend({}, defaults, config);\n\n        var $popover = $tooltip(element, options);\n\n        // Support scope as string options [/*title, */content]\n        if(options.content) {\n          $popover.$scope.content = options.content;\n        }\n\n        return $popover;\n\n      }\n\n      return PopoverFactory;\n\n    };\n\n  })\n\n  .directive('bsPopover', function($window, $sce, $popover) {\n\n    var requestAnimationFrame = $window.requestAnimationFrame || $window.setTimeout;\n\n    return {\n      restrict: 'EAC',\n      scope: true,\n      link: function postLink(scope, element, attr) {\n\n        // Directive options\n        var options = {scope: scope};\n        angular.forEach(['template', 'contentTemplate', 'placement', 'container', 'target', 'delay', 'trigger', 'keyboard', 'html', 'animation', 'customClass', 'autoClose', 'id'], function(key) {\n          if(angular.isDefined(attr[key])) options[key] = attr[key];\n        });\n\n        // Support scope as data-attrs\n        angular.forEach(['title', 'content'], function(key) {\n          attr[key] && attr.$observe(key, function(newValue, oldValue) {\n            scope[key] = $sce.trustAsHtml(newValue);\n            angular.isDefined(oldValue) && requestAnimationFrame(function() {\n              popover && popover.$applyPlacement();\n            });\n          });\n        });\n\n        // Support scope as an object\n        attr.bsPopover && scope.$watch(attr.bsPopover, function(newValue, oldValue) {\n          if(angular.isObject(newValue)) {\n            angular.extend(scope, newValue);\n          } else {\n            scope.content = newValue;\n          }\n          angular.isDefined(oldValue) && requestAnimationFrame(function() {\n            popover && popover.$applyPlacement();\n          });\n        }, true);\n\n        // Visibility binding support\n        attr.bsShow && scope.$watch(attr.bsShow, function(newValue, oldValue) {\n          if(!popover || !angular.isDefined(newValue)) return;\n          if(angular.isString(newValue)) newValue = !!newValue.match(/true|,?(popover),?/i);\n          newValue === true ? popover.show() : popover.hide();\n        });\n\n        // Initialize popover\n        var popover = $popover(element, options);\n\n        // Garbage collection\n        scope.$on('$destroy', function() {\n          if (popover) popover.destroy();\n          options = null;\n          popover = null;\n        });\n\n      }\n    };\n\n  });\n"], "sourceRoot": "/source/"}