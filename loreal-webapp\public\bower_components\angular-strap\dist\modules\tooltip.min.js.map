{"version": 3, "sources": ["tooltip/tooltip.js"], "names": [], "mappings": "qBASM,OAAA,0BAAa,+CAEb,WAAW,cAEX,GAAW,KAAA,UACX,UAAU,UACV,YAAA,GACA,YAAS,UACT,YAAU,UACV,WAAM,EACN,QAAM,EACN,UAAO,MACP,SAAM,2BACN,iBAAO,EACP,QAAA,cACA,UAAA,kBAGF,MAAK,WAEH,MAAI,EACJ,WAAI,EACJ,WAAI,QAGJ,MAAS,UAAA,aAAwB,WAAQ,KAAA,iBAAA,QAAA,WAAA,OAAA,aAAA,QAAA,WAAA,SAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,WAOvC,GAAoB,EAAA,WA+MlB,KACA,EAAK,MAAA,EAAQ,YAAkB,QAAY,WAkDtC,UACD,MAAO,EAAQ,YAAG,QAAA,GAIpB,IAAA,EAAA,2DAuGE,QACA,GAAA,EAAa,QAAY,MAAA,qFAK/B,EAAS,GAAA,UAAA,EAAsB,aAAA,QAAA,EAAA,OACzB,EAAA,GAAmB,UAAR,EAAsB,aAAA,OAAA,EAAA,OACxB,WAAb,GAAoC,UAAd,GAAc,EAAA,GAAA,EAAA,aAAA,YAAA,EAAA,qCAKhC,SAEA,GADA,GAAQ,EAAI,QAAY,MAAA,KACxB,EAAA,EAAa,OAAY,KAAA,+DAK/B,EAAS,IAAqB,UAArB,EAAqB,aAAA,QAAA,EAAA,OACzB,EAAQ,IAAY,UAAZ,EAAqB,aAAA,OAAA,EAAA,OAChB,WAAd,GAAgC,UAAT,GAAS,EAAA,IAAA,EAAA,aAAA,YAAA,EAAA,yCAM3B,UAAT,EAAS,QACP,EAAW,GAAA,QAAY,EAAS,YAEzB,GAAA,QAAA,EAAA,4BAKL,UAAA,EAAA,QACJ,EAAS,IAAA,QAAA,EAAsB,wDAU3B,2BAKF,EAAI,GAAA,QAAA,EAAwB,MAE1B,GAAmB,GACnB,GAAA,gBAIJ,IACE,EAAM,IAAA,QAAA,uCAMN,GAAwB,uCAOb,GAAQ,EAAW,QAAU,aAIpC,EAAQ,EAAA,uBACS,QAAnB,EAAQ,QAER,EAAQ,QAAA,UAAoB,GAAA,MAAA,EAAA,MAAA,EAAA,KAAA,OAAA,EAAA,OAAA,EAAA,MAG9B,IAAA,EAOA,2CAHI,EAAA,SAAA,GAGJ,QAAc,UAAA,EAAA,WAGV,GAAoB,EAAS,EAAa,EAAe,MACzD,iBAGJ,QAAK,EAAA,QACH,WAEE,IAAA,EAAM,IAAS,EAAO,OAAS,EAAQ,EAAkB,sBAG7D,WACE,YAEE,IAAA,EAAM,IAAS,EAAO,iCAG1B,WACE,UAEE,IAAA,EAAM,IAAS,EAAO,OAAS,EAAQ,EAAkB,+BAK7D,GACE,IAAO,EAAA,IAAA,iCAMP,EAAK,SACH,2CAKM,EAAM,IACd,IAAA,OACA,EAAK,KAAA,EAAA,IACH,WACA,QACF,EAAK,KAAA,EAAA,KAAA,EAAA,MAAA,+DAKP,EAAO,IAAA,EAAA,IAAA,qBAGT,EAAS,IAAA,EAAkB,IAAK,EAAM,OAItC,MAAS,mBAIP,EAAG,KAAS,IAAA,EAAY,KAAA,KAAe,EAAM,iCAKhC,2CAOX,EAAW,eAKX,2DAphBJ,GAAG,yLAQH,GAAA,GAAS,EAAM,MAAQ,MAAM,KAAQ,IAAK,yFAUtC,0CAKA,YAAS,SAAA,kDAKT,MAAS,mDAKT,MAAS,gGAOT,EAAS,cAIX,SAAS,EAAW,UAAS,QAGrB,IAGJ,oBACA,SAAO,EAAc,SAAA,KAAA,SAAA,4GAO3B,kDADI,EAAW,WAAY,WAAA,KAAa,GAC/B,EAAc,GAAA,kBAMrB,GAAS,EAAA,EAAA,EAAA,8DAGX,EAAS,OAAO,EAAW,EAAA,QAAA,EAAA,oCAGzB,EAAY,EAAS,KACnB,8HAqBF,QAAA,UAAA,EAAA,uCAGA,EAAW,EAAQ,EAAA,kBAOf,4EAMN,EAAS,aAAU,iFAanB,IAGE,EAAA,mDAMM,KACK,gCAKX,EAAK,WAAQ,+BAEb,EAAM,MAAM,mBAIN,KAAA,cACF,EAAQ,YAAQ,EAAQ,YAExB,MAAQ,EAAA,YAAA,eAAA,aAEL,WACL,EAAS,IACT,EAAQ,GAAA,wDAOV,EAAA,GAKA,GAAe,sBAGf,EAAW,EAAW,SAAW,EAAS,EAAQ,cAIlD,EAAW,KAAA,IAAA,UAAa,KAAW,UAAS,QAAQ,QAAA,WAAA,gDAKjD,EAAA,MAAW,EAAc,SAAQ,EAAK,YAAA,IAAA,EAAA,MAEzC,EAAS,aAAW,EAAM,SAAW,EAAA,oGAUrC,EAAW,qDAOR,EAAQ,WACT,UAAA,EAAA,wBAMF,EAAM,mBAUN,MAAU,+BAEG,WAEV,EAAQ,OAAM,EAAA,MAAA,kCAIf,QAAA,GACA,EAAA,uCAOF,oCAIA,EAAA,MAAA,EAAa,YAAA,eAAA,OAOb,EAAS,CAIT,IAAG,GAAQ,EAAY,MAAA,EAAe,EACpC,IAAA,EAAA,MAAA,EAAA,KAAA,4BAGF,EAAW,8EA6Bb,EAAS,SAAa,EAAS,QAAA,EAAW,4CAOpC,WAAY,SAAA,+CAOhB,GAAI,EAAJ,+GAiBE,GAAgB,IAChB,EAAI,EAAoB,KAAA,2CAItB,EAAA,OACS,EACT,EAAY,EAAA,UAAkB,QAAQ,QAAO,SAAA,cAAA,EAAA,YAAA,EAAA,wFAM1C,EAAsB,QAAA,QAAW,GAAA,EAAsB,IAAA,EAAiB,EAAsB,QAC/F,EAAwB,QAAW,MAAA,YAMzB,UAAZ,GAAqD,gBAAnB,GAA8C,aAAT,sBAGzE,EAAuB,UAAZ,EAA+B,OAAS,EAAA,QAAA,OAAA,8EAIjD,EAAkC,SAAlC,EAAkC,QAAW,EAAiB,QAAA,QAAU,yCAO1E,GAAI,EAAA,EAAA,EAAA,EAAA,qBAIR,EAAS,SAAA,SAAgB,GACL,KAAd,EAAI,OAAc,EAAA,WACpB,EAAQ,OACR,EAAI,oBAIR,EAAS,cAAA,SAAA,GACH,KAAA,EAAA,QACJ,EAAI,GAAA,oEAMN,EAAA,iBACE,EAAI,oBAEC,SAAY,EAAS,GAAA,OAAA,EAAA,GAAA,iBAuL5B,OAAM,iBAOR,EAAI,SAAgB,EAAA,OAAA,EAAA,MAAA,SAAA,EAAA,kBAGlB,GAAQ,EAAc,SAChB,SAAS,SAAK,GAAA,UAAA,iBAAA,wBAKlB,GAAO,GAAA,EAAA,gFAIX,EAAO,IAAA,EAAA,EAAA,qBA1jBL,GAAI,OAAW,UAAA,kDAGf,EAAI,QAAW,QAAW,EAAA,uIAukBrB,oCAKL,GAAI,IAAO,MAAA,WACH,SAAQ,WAAA,kBAAA,YAAA,YAAA,SAAA,QAAA,UAAA,WAAA,OAAA,YAAA,oBAAA,OAAA,cAAA,MAAA,SAAA,0CAMZ,EAAI,eAAiB,aACrB,MAAM,+HAQV,QAAK,UAAa,IAAa,EAAK,WAC/B,GAAQ,EAAS,yBAMlB,WAAW,EAAQ,OAAA,EAAA,UAAA,SAAA,EAAA,uBAEpB,QAAA,OAAA,EAAA,aAID,QAAI,UAAY,IAAQ,EAAU,WAClC,GAAW,EAAS,yBAKtB,EAAK,QAAA,EAAa,OAAM,EAAO,OAAK,SAAW,6BAE1C,QAAC,SAAY,KAAQ,IAAU,EAAW,MAAA,wBAC7C,KAAW,EAAS,EAAA,OAAW,EAAa,8IAQxB,EAAA,WAAtB,KAAU,GAAuB,GAAA,8DASpC,EAAA", "file": "tooltip.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.tooltip', ['mgcrea.ngStrap.helpers.dimensions'])\n\n  .provider('$tooltip', function() {\n\n    var defaults = this.defaults = {\n      animation: 'am-fade',\n      customClass: '',\n      prefixClass: 'tooltip',\n      prefixEvent: 'tooltip',\n      container: false,\n      target: false,\n      placement: 'top',\n      template: 'tooltip/tooltip.tpl.html',\n      contentTemplate: false,\n      trigger: 'hover focus',\n      keyboard: false,\n      html: false,\n      show: false,\n      title: '',\n      type: '',\n      delay: 0,\n      autoClose: false,\n      bsEnabled: true\n    };\n\n    this.$get = function($window, $rootScope, $compile, $q, $templateCache, $http, $animate, $sce, dimensions, $$rAF, $timeout) {\n\n      var trim = String.prototype.trim;\n      var isTouch = 'createTouch' in $window.document;\n      var htmlReplaceRegExp = /ng-bind=\"/ig;\n      var $body = angular.element($window.document);\n\n      function TooltipFactory(element, config) {\n\n        var $tooltip = {};\n\n        // Common vars\n        var nodeName = element[0].nodeName.toLowerCase();\n        var options = $tooltip.$options = angular.extend({}, defaults, config);\n        $tooltip.$promise = fetchTemplate(options.template);\n        var scope = $tooltip.$scope = options.scope && options.scope.$new() || $rootScope.$new();\n        if(options.delay && angular.isString(options.delay)) {\n          var split = options.delay.split(',').map(parseFloat);\n          options.delay = split.length > 1 ? {show: split[0], hide: split[1]} : split[0];\n        }\n\n        // store $id to identify the triggering element in events\n        // give priority to options.id, otherwise, try to use\n        // element id if defined\n        $tooltip.$id = options.id || element.attr('id') || '';\n\n        // Support scope as string options\n        if(options.title) {\n          scope.title = $sce.trustAsHtml(options.title);\n        }\n\n        // Provide scope helpers\n        scope.$setEnabled = function(isEnabled) {\n          scope.$$postDigest(function() {\n            $tooltip.setEnabled(isEnabled);\n          });\n        };\n        scope.$hide = function() {\n          scope.$$postDigest(function() {\n            $tooltip.hide();\n          });\n        };\n        scope.$show = function() {\n          scope.$$postDigest(function() {\n            $tooltip.show();\n          });\n        };\n        scope.$toggle = function() {\n          scope.$$postDigest(function() {\n            $tooltip.toggle();\n          });\n        };\n        // Publish isShown as a protected var on scope\n        $tooltip.$isShown = scope.$isShown = false;\n\n        // Private vars\n        var timeout, hoverState;\n\n        // Support contentTemplate option\n        if(options.contentTemplate) {\n          $tooltip.$promise = $tooltip.$promise.then(function(template) {\n            var templateEl = angular.element(template);\n            return fetchTemplate(options.contentTemplate)\n            .then(function(contentTemplate) {\n              var contentEl = findElement('[ng-bind=\"content\"]', templateEl[0]);\n              if(!contentEl.length) contentEl = findElement('[ng-bind=\"title\"]', templateEl[0]);\n              contentEl.removeAttr('ng-bind').html(contentTemplate);\n              return templateEl[0].outerHTML;\n            });\n          });\n        }\n\n        // Fetch, compile then initialize tooltip\n        var tipLinker, tipElement, tipTemplate, tipContainer, tipScope;\n        $tooltip.$promise.then(function(template) {\n          if(angular.isObject(template)) template = template.data;\n          if(options.html) template = template.replace(htmlReplaceRegExp, 'ng-bind-html=\"');\n          template = trim.apply(template);\n          tipTemplate = template;\n          tipLinker = $compile(template);\n          $tooltip.init();\n        });\n\n        $tooltip.init = function() {\n\n          // Options: delay\n          if (options.delay && angular.isNumber(options.delay)) {\n            options.delay = {\n              show: options.delay,\n              hide: options.delay\n            };\n          }\n\n          // Replace trigger on touch devices ?\n          // if(isTouch && options.trigger === defaults.trigger) {\n          //   options.trigger.replace(/hover/g, 'click');\n          // }\n\n          // Options : container\n          if(options.container === 'self') {\n            tipContainer = element;\n          } else if(angular.isElement(options.container)) {\n            tipContainer = options.container;\n          } else if(options.container) {\n            tipContainer = findElement(options.container);\n          }\n\n          // Options: trigger\n          bindTriggerEvents();\n\n          // Options: target\n          if(options.target) {\n            options.target = angular.isElement(options.target) ? options.target : findElement(options.target);\n          }\n\n          // Options: show\n          if(options.show) {\n            scope.$$postDigest(function() {\n              options.trigger === 'focus' ? element[0].focus() : $tooltip.show();\n            });\n          }\n\n        };\n\n        $tooltip.destroy = function() {\n\n          // Unbind events\n          unbindTriggerEvents();\n\n          // Remove element\n          destroyTipElement();\n\n          // Destroy scope\n          scope.$destroy();\n\n        };\n\n        $tooltip.enter = function() {\n\n          clearTimeout(timeout);\n          hoverState = 'in';\n          if (!options.delay || !options.delay.show) {\n            return $tooltip.show();\n          }\n\n          timeout = setTimeout(function() {\n            if (hoverState ==='in') $tooltip.show();\n          }, options.delay.show);\n\n        };\n\n        $tooltip.show = function() {\n          if (!options.bsEnabled || $tooltip.$isShown) return;\n\n          scope.$emit(options.prefixEvent + '.show.before', $tooltip);\n          var parent, after;\n          if (options.container) {\n            parent = tipContainer;\n            if (tipContainer[0].lastChild) {\n              after = angular.element(tipContainer[0].lastChild);\n            } else {\n              after = null;\n            }\n          } else {\n            parent = null;\n            after = element;\n          }\n\n\n          // Hide any existing tipElement\n          if(tipElement) destroyTipElement();\n          // Fetch a cloned element linked from template\n          tipScope = $tooltip.$scope.$new();\n          tipElement = $tooltip.$element = tipLinker(tipScope, function(clonedElement, scope) {});\n\n          // Set the initial positioning.  Make the tooltip invisible\n          // so IE doesn't try to focus on it off screen.\n          tipElement.css({top: '-9999px', left: '-9999px', display: 'block', visibility: 'hidden'});\n\n          // Options: animation\n          if(options.animation) tipElement.addClass(options.animation);\n          // Options: type\n          if(options.type) tipElement.addClass(options.prefixClass + '-' + options.type);\n          // Options: custom classes\n          if(options.customClass) tipElement.addClass(options.customClass);\n\n          // Support v1.3+ $animate\n          // https://github.com/angular/angular.js/commit/bf0f5502b1bbfddc5cdd2f138efd9188b8c652a9\n          var promise = $animate.enter(tipElement, parent, after, enterAnimateCallback);\n          if(promise && promise.then) promise.then(enterAnimateCallback);\n\n          $tooltip.$isShown = scope.$isShown = true;\n          safeDigest(scope);\n          $$rAF(function () {\n            $tooltip.$applyPlacement();\n\n            // Once placed, make the tooltip visible\n            if(tipElement) tipElement.css({visibility: 'visible'});\n          }); // var a = bodyEl.offsetWidth + 1; ?\n\n          // Bind events\n          if(options.keyboard) {\n            if(options.trigger !== 'focus') {\n              $tooltip.focus();\n            }\n            bindKeyboardEvents();\n          }\n\n          if(options.autoClose) {\n            bindAutoCloseEvents();\n          }\n\n        };\n\n        function enterAnimateCallback() {\n          scope.$emit(options.prefixEvent + '.show', $tooltip);\n        }\n\n        $tooltip.leave = function() {\n\n          clearTimeout(timeout);\n          hoverState = 'out';\n          if (!options.delay || !options.delay.hide) {\n            return $tooltip.hide();\n          }\n          timeout = setTimeout(function () {\n            if (hoverState === 'out') {\n              $tooltip.hide();\n            }\n          }, options.delay.hide);\n\n        };\n\n        var _blur;\n        var _tipToHide;\n        $tooltip.hide = function(blur) {\n\n          if(!$tooltip.$isShown) return;\n          scope.$emit(options.prefixEvent + '.hide.before', $tooltip);\n\n          // store blur value for leaveAnimateCallback to use\n          _blur = blur;\n\n          // store current tipElement reference to use\n          // in leaveAnimateCallback\n          _tipToHide = tipElement;\n\n          // Support v1.3+ $animate\n          // https://github.com/angular/angular.js/commit/bf0f5502b1bbfddc5cdd2f138efd9188b8c652a9\n          var promise = $animate.leave(tipElement, leaveAnimateCallback);\n          if(promise && promise.then) promise.then(leaveAnimateCallback);\n\n          $tooltip.$isShown = scope.$isShown = false;\n          safeDigest(scope);\n\n          // Unbind events\n          if(options.keyboard && tipElement !== null) {\n            unbindKeyboardEvents();\n          }\n\n          if(options.autoClose && tipElement !== null) {\n            unbindAutoCloseEvents();\n          }\n        };\n\n        function leaveAnimateCallback() {\n          scope.$emit(options.prefixEvent + '.hide', $tooltip);\n\n          // check if current tipElement still references\n          // the same element when hide was called\n          if (tipElement === _tipToHide) {\n            // Allow to blur the input when hidden, like when pressing enter key\n            if(_blur && options.trigger === 'focus') {\n              return element[0].blur();\n            }\n\n            // clean up child scopes\n            destroyTipElement();\n          }\n        }\n\n        $tooltip.toggle = function() {\n          $tooltip.$isShown ? $tooltip.leave() : $tooltip.enter();\n        };\n\n        $tooltip.focus = function() {\n          tipElement[0].focus();\n        };\n\n        $tooltip.setEnabled = function(isEnabled) {\n          options.bsEnabled = isEnabled;\n        };\n\n        // Protected methods\n\n        $tooltip.$applyPlacement = function() {\n          if(!tipElement) return;\n\n          // Determine if we're doing an auto or normal placement\n          var placement = options.placement,\n              autoToken = /\\s?auto?\\s?/i,\n              autoPlace  = autoToken.test(placement);\n\n          if (autoPlace) {\n            placement = placement.replace(autoToken, '') || defaults.placement;\n          }\n\n          // Need to add the position class before we get\n          // the offsets\n          tipElement.addClass(options.placement);\n\n          // Get the position of the target element\n          // and the height and width of the tooltip so we can center it.\n          var elementPosition = getPosition(),\n              tipWidth = tipElement.prop('offsetWidth'),\n              tipHeight = tipElement.prop('offsetHeight');\n\n          // If we're auto placing, we need to check the positioning\n          if (autoPlace) {\n            var originalPlacement = placement;\n            var container = options.container ? angular.element(document.querySelector(options.container)) : element.parent();\n            var containerPosition = getPosition(container);\n\n            // Determine if the vertical placement\n            if (originalPlacement.indexOf('bottom') >= 0 && elementPosition.bottom + tipHeight > containerPosition.bottom) {\n              placement = originalPlacement.replace('bottom', 'top');\n            } else if (originalPlacement.indexOf('top') >= 0 && elementPosition.top - tipHeight < containerPosition.top) {\n              placement = originalPlacement.replace('top', 'bottom');\n            }\n\n            // Determine the horizontal placement\n            // The exotic placements of left and right are opposite of the standard placements.  Their arrows are put on the left/right\n            // and flow in the opposite direction of their placement.\n            if ((originalPlacement === 'right' || originalPlacement === 'bottom-left' || originalPlacement === 'top-left') &&\n                elementPosition.right + tipWidth > containerPosition.width) {\n\n              placement = originalPlacement === 'right' ? 'left' : placement.replace('left', 'right');\n            } else if ((originalPlacement === 'left' || originalPlacement === 'bottom-right' || originalPlacement === 'top-right') &&\n                elementPosition.left - tipWidth < containerPosition.left) {\n\n              placement = originalPlacement === 'left' ? 'right' : placement.replace('right', 'left');\n            }\n\n            tipElement.removeClass(originalPlacement).addClass(placement);\n          }\n\n          // Get the tooltip's top and left coordinates to center it with this directive.\n          var tipPosition = getCalculatedOffset(placement, elementPosition, tipWidth, tipHeight);\n          applyPlacementCss(tipPosition.top, tipPosition.left);\n        };\n\n        $tooltip.$onKeyUp = function(evt) {\n          if (evt.which === 27 && $tooltip.$isShown) {\n            $tooltip.hide();\n            evt.stopPropagation();\n          }\n        };\n\n        $tooltip.$onFocusKeyUp = function(evt) {\n          if (evt.which === 27) {\n            element[0].blur();\n            evt.stopPropagation();\n          }\n        };\n\n        $tooltip.$onFocusElementMouseDown = function(evt) {\n          evt.preventDefault();\n          evt.stopPropagation();\n          // Some browsers do not auto-focus buttons (eg. Safari)\n          $tooltip.$isShown ? element[0].blur() : element[0].focus();\n        };\n\n        // bind/unbind events\n        function bindTriggerEvents() {\n          var triggers = options.trigger.split(' ');\n          angular.forEach(triggers, function(trigger) {\n            if(trigger === 'click') {\n              element.on('click', $tooltip.toggle);\n            } else if(trigger !== 'manual') {\n              element.on(trigger === 'hover' ? 'mouseenter' : 'focus', $tooltip.enter);\n              element.on(trigger === 'hover' ? 'mouseleave' : 'blur', $tooltip.leave);\n              nodeName === 'button' && trigger !== 'hover' && element.on(isTouch ? 'touchstart' : 'mousedown', $tooltip.$onFocusElementMouseDown);\n            }\n          });\n        }\n\n        function unbindTriggerEvents() {\n          var triggers = options.trigger.split(' ');\n          for (var i = triggers.length; i--;) {\n            var trigger = triggers[i];\n            if(trigger === 'click') {\n              element.off('click', $tooltip.toggle);\n            } else if(trigger !== 'manual') {\n              element.off(trigger === 'hover' ? 'mouseenter' : 'focus', $tooltip.enter);\n              element.off(trigger === 'hover' ? 'mouseleave' : 'blur', $tooltip.leave);\n              nodeName === 'button' && trigger !== 'hover' && element.off(isTouch ? 'touchstart' : 'mousedown', $tooltip.$onFocusElementMouseDown);\n            }\n          }\n        }\n\n        function bindKeyboardEvents() {\n          if(options.trigger !== 'focus') {\n            tipElement.on('keyup', $tooltip.$onKeyUp);\n          } else {\n            element.on('keyup', $tooltip.$onFocusKeyUp);\n          }\n        }\n\n        function unbindKeyboardEvents() {\n          if(options.trigger !== 'focus') {\n            tipElement.off('keyup', $tooltip.$onKeyUp);\n          } else {\n            element.off('keyup', $tooltip.$onFocusKeyUp);\n          }\n        }\n\n        var _autoCloseEventsBinded = false;\n        function bindAutoCloseEvents() {\n          // use timeout to hookup the events to prevent\n          // event bubbling from being processed imediately.\n          $timeout(function() {\n            // Stop propagation when clicking inside tooltip\n            tipElement.on('click', stopEventPropagation);\n\n            // Hide when clicking outside tooltip\n            $body.on('click', $tooltip.hide);\n\n            _autoCloseEventsBinded = true;\n          }, 0, false);\n        }\n\n        function unbindAutoCloseEvents() {\n          if (_autoCloseEventsBinded) {\n            tipElement.off('click', stopEventPropagation);\n            $body.off('click', $tooltip.hide);\n            _autoCloseEventsBinded = false;\n          }\n        }\n\n        function stopEventPropagation(event) {\n          event.stopPropagation();\n        }\n\n        // Private methods\n\n        function getPosition($element) {\n          $element = $element || (options.target || element);\n\n          var el = $element[0];\n\n          var elRect = el.getBoundingClientRect();\n          if (elRect.width === null) {\n            // width and height are missing in IE8, so compute them manually; see https://github.com/twbs/bootstrap/issues/14093\n            elRect = angular.extend({}, elRect, { width: elRect.right - elRect.left, height: elRect.bottom - elRect.top });\n          }\n\n          var elPos;\n          if (options.container === 'body') {\n            elPos = dimensions.offset(el);\n          } else {\n            elPos = dimensions.position(el);\n          }\n\n          return angular.extend({}, elRect, elPos);\n        }\n\n        function getCalculatedOffset(placement, position, actualWidth, actualHeight) {\n          var offset;\n          var split = placement.split('-');\n\n          switch (split[0]) {\n          case 'right':\n            offset = {\n              top: position.top + position.height / 2 - actualHeight / 2,\n              left: position.left + position.width\n            };\n            break;\n          case 'bottom':\n            offset = {\n              top: position.top + position.height,\n              left: position.left + position.width / 2 - actualWidth / 2\n            };\n            break;\n          case 'left':\n            offset = {\n              top: position.top + position.height / 2 - actualHeight / 2,\n              left: position.left - actualWidth\n            };\n            break;\n          default:\n            offset = {\n              top: position.top - actualHeight,\n              left: position.left + position.width / 2 - actualWidth / 2\n            };\n            break;\n          }\n\n          if(!split[1]) {\n            return offset;\n          }\n\n          // Add support for corners @todo css\n          if(split[0] === 'top' || split[0] === 'bottom') {\n            switch (split[1]) {\n            case 'left':\n              offset.left = position.left;\n              break;\n            case 'right':\n              offset.left =  position.left + position.width - actualWidth;\n            }\n          } else if(split[0] === 'left' || split[0] === 'right') {\n            switch (split[1]) {\n            case 'top':\n              offset.top = position.top - actualHeight;\n              break;\n            case 'bottom':\n              offset.top = position.top + position.height;\n            }\n          }\n\n          return offset;\n        }\n\n        function applyPlacementCss(top, left) {\n          tipElement.css({ top: top + 'px', left: left + 'px' });\n        }\n\n        function destroyTipElement() {\n          // Cancel pending callbacks\n          clearTimeout(timeout);\n\n          if($tooltip.$isShown && tipElement !== null) {\n            if(options.autoClose) {\n              unbindAutoCloseEvents();\n            }\n\n            if(options.keyboard) {\n              unbindKeyboardEvents();\n            }\n          }\n\n          if(tipScope) {\n            tipScope.$destroy();\n            tipScope = null;\n          }\n\n          if(tipElement) {\n            tipElement.remove();\n            tipElement = $tooltip.$element = null;\n          }\n        }\n\n        return $tooltip;\n\n      }\n\n      // Helper functions\n\n      function safeDigest(scope) {\n        scope.$$phase || (scope.$root && scope.$root.$$phase) || scope.$digest();\n      }\n\n      function findElement(query, element) {\n        return angular.element((element || document).querySelectorAll(query));\n      }\n\n      var fetchPromises = {};\n      function fetchTemplate(template) {\n        if(fetchPromises[template]) return fetchPromises[template];\n        return (fetchPromises[template] = $q.when($templateCache.get(template) || $http.get(template))\n        .then(function(res) {\n          if(angular.isObject(res)) {\n            $templateCache.put(template, res.data);\n            return res.data;\n          }\n          return res;\n        }));\n      }\n\n      return TooltipFactory;\n\n    };\n\n  })\n\n  .directive('bsTooltip', function($window, $location, $sce, $tooltip, $$rAF) {\n\n    return {\n      restrict: 'EAC',\n      scope: true,\n      link: function postLink(scope, element, attr, transclusion) {\n\n        // Directive options\n        var options = {scope: scope};\n        angular.forEach(['template', 'contentTemplate', 'placement', 'container', 'target', 'delay', 'trigger', 'keyboard', 'html', 'animation', 'backdropAnimation', 'type', 'customClass', 'id'], function(key) {\n          if(angular.isDefined(attr[key])) options[key] = attr[key];\n        });\n\n        // overwrite inherited title value when no value specified\n        // fix for angular 1.3.1 531a8de72c439d8ddd064874bf364c00cedabb11\n        if (!scope.hasOwnProperty('title')){\n          scope.title = '';\n        }\n\n        // Observe scope attributes for change\n        attr.$observe('title', function(newValue) {\n          if (angular.isDefined(newValue) || !scope.hasOwnProperty('title')) {\n            var oldValue = scope.title;\n            scope.title = $sce.trustAsHtml(newValue);\n            angular.isDefined(oldValue) && $$rAF(function() {\n              tooltip && tooltip.$applyPlacement();\n            });\n          }\n        });\n\n        // Support scope as an object\n        attr.bsTooltip && scope.$watch(attr.bsTooltip, function(newValue, oldValue) {\n          if(angular.isObject(newValue)) {\n            angular.extend(scope, newValue);\n          } else {\n            scope.title = newValue;\n          }\n          angular.isDefined(oldValue) && $$rAF(function() {\n            tooltip && tooltip.$applyPlacement();\n          });\n        }, true);\n\n        // Visibility binding support\n        attr.bsShow && scope.$watch(attr.bsShow, function(newValue, oldValue) {\n          if(!tooltip || !angular.isDefined(newValue)) return;\n          if(angular.isString(newValue)) newValue = !!newValue.match(/true|,?(tooltip),?/i);\n          newValue === true ? tooltip.show() : tooltip.hide();\n        });\n\n        // Enabled binding support\n        attr.bsEnabled && scope.$watch(attr.bsEnabled, function(newValue, oldValue) {\n          // console.warn('scope.$watch(%s)', attr.bsEnabled, newValue, oldValue);\n          if(!tooltip || !angular.isDefined(newValue)) return;\n          if(angular.isString(newValue)) newValue = !!newValue.match(/true|1|,?(tooltip),?/i);\n          newValue === false ? tooltip.setEnabled(false) : tooltip.setEnabled(true);\n        });\n\n        // Initialize popover\n        var tooltip = $tooltip(element, options);\n\n        // Garbage collection\n        scope.$on('$destroy', function() {\n          if(tooltip) tooltip.destroy();\n          options = null;\n          tooltip = null;\n        });\n\n      }\n    };\n\n  });\n"], "sourceRoot": "/source/"}