{"version": 3, "sources": ["modal/modal.js"], "names": [], "mappings": "qBASM,OAAA,wBAAa,+CAEb,SAAW,cAEX,GAAA,KAAiB,UACjB,UAAW,UACX,kBAAS,UACT,YAAU,QACV,YAAU,QACV,UAAM,MACN,SAAM,uDAGR,QAAK,iBAEH,UAAI,EACJ,MAAI,EACJ,MAAI,4JAWE,GAAe,WAiKd,OACD,MAAA,EAAA,YAAA,QAAA,gBAgCJ,EAAO,MAAA,EAAS,YAAW,QAAA,wCAEzB,EAAO,wGA8BF,WAAP,EAAO,SAAA,EAAA,QAAA,EAAA,4CAhOL,mHAMF,GAAO,SAAc,EAAM,mFAUvB,QAAO,WAAA,SAAA,wCAKP,MAAO,mDAKP,MAAO,gGAOR,EAAQ,cAIN,SAAK,EAAS,UAAA,IAGT,oBACJ,SAAO,EAAc,SAAA,KAAA,SAAA,0IAOvB,OADA,GAAa,UAAA,EAAA,OAAA,SACb,EAAkB,GAAA,kBAMpB,GAAO,gEAiLT,oCA9KA,QAAO,SAAO,KAAW,EAAA,EAAA,6DAGvB,EAAW,EAAM,KACf,mCAOJ,EAAO,aAAU,yBAOZ,QAAA,yDAaH,EAAG,cAIA,KAAQ,eACT,EAAA,aAEK,MAAA,EAAA,YAAA,eAAA,GAAA,qBAGH,GAAQ,UACH,UAAA,EAAA,cACL,EAAS,YACT,EAAQ,UAAQ,GAAA,UAAA,QAAA,QAAA,EAAA,UAAA,GAAA,WAAA,kHAYP,EAAA,SAAU,EAAA,EAAA,gBAGrB,KAAa,QAAS,UAAQ,SAAA,EAAA,WAG7B,EAAQ,YACT,EAAS,+EAOX,EAAO,MAAA,EAAiB,EAAW,KAInC,IAAI,GAAK,EAAa,MAAA,EAAA,EAAA,EAAA,EACtB,IAAA,EAAsB,MAAA,EAAW,KAAA,oCAM/B,GAAA,EAAY,8BAKZ,SAAgB,EAAS,YAAA,SACzB,EAAA,WACA,EAAA,SAAmB,EAAS,YAAA,SAAA,EAAA,wDAOhC,EAAS,GAAA,QAAuB,+KAsB3B,EAAQ,UACT,EAAA,MAAa,KAEb,SAAgB,EAAI,UAAS,sDAOjC,EAAS,IAAA,QAAuB,IAE9B,EAAA,UACA,EAAW,IAAA,QAAW,EAAA,cAYtB,OAAa,4DAOb,EAAQ,GAAA,oEASV,EAAA,oBAgBM,gBAOR,EAAI,SAAgB,EAAA,OAAA,EAAA,MAAA,SAAA,EAAA,kBAGlB,GAAQ,EAAc,SAChB,SAAS,SAAK,GAAA,UAAA,iBAAA,wBAKlB,GAAO,GAAA,EAAA,gFAIX,EAAO,IAAA,EAAA,EAAA,wCAlQL,EAAI,OAAS,UAAA,gFAGb,EAAqB,+GA+QhB,uCAKH,IAAa,MAAK,EAAS,QAAK,EAAS,MAAU,WACjD,SAAa,WAAK,kBAAY,YAAA,WAAA,WAAA,OAAA,YAAA,YAAA,MAAA,SAAA,0CAKlC,QAAK,SAAW,QAAM,WAAY,SAAS,GACzC,EAAG,IAAQ,EAAA,SAAS,EAAW,SAAA,GAC7B,EAAA,GAAQ,EAAO,YAAO,qEAOtB,QAAQ,OAAO,EAAA,uBAOjB,GAAI,EAAa,gFAQtB,EAAA", "file": "modal.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.modal', ['mgcrea.ngStrap.helpers.dimensions'])\n\n  .provider('$modal', function() {\n\n    var defaults = this.defaults = {\n      animation: 'am-fade',\n      backdropAnimation: 'am-fade',\n      prefixClass: 'modal',\n      prefixEvent: 'modal',\n      placement: 'top',\n      template: 'modal/modal.tpl.html',\n      contentTemplate: false,\n      container: false,\n      element: null,\n      backdrop: true,\n      keyboard: true,\n      html: false,\n      show: true\n    };\n\n    this.$get = function($window, $rootScope, $compile, $q, $templateCache, $http, $animate, $timeout, $sce, dimensions) {\n\n      var forEach = angular.forEach;\n      var trim = String.prototype.trim;\n      var requestAnimationFrame = $window.requestAnimationFrame || $window.setTimeout;\n      var bodyElement = angular.element($window.document.body);\n      var htmlReplaceRegExp = /ng-bind=\"/ig;\n\n      function ModalFactory(config) {\n\n        var $modal = {};\n\n        // Common vars\n        var options = $modal.$options = angular.extend({}, defaults, config);\n        $modal.$promise = fetchTemplate(options.template);\n        var scope = $modal.$scope = options.scope && options.scope.$new() || $rootScope.$new();\n        if(!options.element && !options.container) {\n          options.container = 'body';\n        }\n\n        // store $id to identify the triggering element in events\n        // give priority to options.id, otherwise, try to use\n        // element id if defined\n        $modal.$id = options.id || options.element && options.element.attr('id') || '';\n\n        // Support scope as string options\n        forEach(['title', 'content'], function(key) {\n          if(options[key]) scope[key] = $sce.trustAsHtml(options[key]);\n        });\n\n        // Provide scope helpers\n        scope.$hide = function() {\n          scope.$$postDigest(function() {\n            $modal.hide();\n          });\n        };\n        scope.$show = function() {\n          scope.$$postDigest(function() {\n            $modal.show();\n          });\n        };\n        scope.$toggle = function() {\n          scope.$$postDigest(function() {\n            $modal.toggle();\n          });\n        };\n        // Publish isShown as a protected var on scope\n        $modal.$isShown = scope.$isShown = false;\n\n        // Support contentTemplate option\n        if(options.contentTemplate) {\n          $modal.$promise = $modal.$promise.then(function(template) {\n            var templateEl = angular.element(template);\n            return fetchTemplate(options.contentTemplate)\n            .then(function(contentTemplate) {\n              var contentEl = findElement('[ng-bind=\"content\"]', templateEl[0]).removeAttr('ng-bind').html(contentTemplate);\n              // Drop the default footer as you probably don't want it if you use a custom contentTemplate\n              if(!config.template) contentEl.next().remove();\n              return templateEl[0].outerHTML;\n            });\n          });\n        }\n\n        // Fetch, compile then initialize modal\n        var modalLinker, modalElement;\n        var backdropElement = angular.element('<div class=\"' + options.prefixClass + '-backdrop\"/>');\n        $modal.$promise.then(function(template) {\n          if(angular.isObject(template)) template = template.data;\n          if(options.html) template = template.replace(htmlReplaceRegExp, 'ng-bind-html=\"');\n          template = trim.apply(template);\n          modalLinker = $compile(template);\n          $modal.init();\n        });\n\n        $modal.init = function() {\n\n          // Options: show\n          if(options.show) {\n            scope.$$postDigest(function() {\n              $modal.show();\n            });\n          }\n\n        };\n\n        $modal.destroy = function() {\n\n          // Remove element\n          if(modalElement) {\n            modalElement.remove();\n            modalElement = null;\n          }\n          if(backdropElement) {\n            backdropElement.remove();\n            backdropElement = null;\n          }\n\n          // Destroy scope\n          scope.$destroy();\n\n        };\n\n        $modal.show = function() {\n          if($modal.$isShown) return;\n\n          if(scope.$emit(options.prefixEvent + '.show.before', $modal).defaultPrevented) {\n            return;\n          }\n          var parent, after;\n          if(angular.isElement(options.container)) {\n            parent = options.container;\n            after = options.container[0].lastChild ? angular.element(options.container[0].lastChild) : null;\n          } else {\n            if (options.container) {\n              parent = findElement(options.container);\n              after = parent[0].lastChild ? angular.element(parent[0].lastChild) : null;\n            } else {\n              parent = null;\n              after = options.element;\n            }\n          }\n\n          // Fetch a cloned element linked from template\n          modalElement = $modal.$element = modalLinker(scope, function(clonedElement, scope) {});\n\n          // Set the initial positioning.\n          modalElement.css({display: 'block'}).addClass(options.placement);\n\n          // Options: animation\n          if(options.animation) {\n            if(options.backdrop) {\n              backdropElement.addClass(options.backdropAnimation);\n            }\n            modalElement.addClass(options.animation);\n          }\n\n          if(options.backdrop) {\n            $animate.enter(backdropElement, bodyElement, null);\n          }\n          // Support v1.3+ $animate\n          // https://github.com/angular/angular.js/commit/bf0f5502b1bbfddc5cdd2f138efd9188b8c652a9\n          var promise = $animate.enter(modalElement, parent, after, enterAnimateCallback);\n          if(promise && promise.then) promise.then(enterAnimateCallback);\n\n          $modal.$isShown = scope.$isShown = true;\n          safeDigest(scope);\n          // Focus once the enter-animation has started\n          // Weird PhantomJS bug hack\n          var el = modalElement[0];\n          requestAnimationFrame(function() {\n            el.focus();\n          });\n\n          bodyElement.addClass(options.prefixClass + '-open');\n          if(options.animation) {\n            bodyElement.addClass(options.prefixClass + '-with-' + options.animation);\n          }\n\n          // Bind events\n          if(options.backdrop) {\n            modalElement.on('click', hideOnBackdropClick);\n            backdropElement.on('click', hideOnBackdropClick);\n            backdropElement.on('wheel', preventEventDefault);\n          }\n          if(options.keyboard) {\n            modalElement.on('keyup', $modal.$onKeyUp);\n          }\n        };\n\n        function enterAnimateCallback() {\n          scope.$emit(options.prefixEvent + '.show', $modal);\n        }\n\n        $modal.hide = function() {\n          if(!$modal.$isShown) return;\n\n          if(scope.$emit(options.prefixEvent + '.hide.before', $modal).defaultPrevented) {\n            return;\n          }\n          var promise = $animate.leave(modalElement, leaveAnimateCallback);\n          // Support v1.3+ $animate\n          // https://github.com/angular/angular.js/commit/bf0f5502b1bbfddc5cdd2f138efd9188b8c652a9\n          if(promise && promise.then) promise.then(leaveAnimateCallback);\n\n          if(options.backdrop) {\n            $animate.leave(backdropElement);\n          }\n          $modal.$isShown = scope.$isShown = false;\n          safeDigest(scope);\n\n          // Unbind events\n          if(options.backdrop) {\n            modalElement.off('click', hideOnBackdropClick);\n            backdropElement.off('click', hideOnBackdropClick);\n            backdropElement.off('wheel', preventEventDefault);\n          }\n          if(options.keyboard) {\n            modalElement.off('keyup', $modal.$onKeyUp);\n          }\n        };\n\n        function leaveAnimateCallback() {\n          scope.$emit(options.prefixEvent + '.hide', $modal);\n          bodyElement.removeClass(options.prefixClass + '-open');\n          if(options.animation) {\n            bodyElement.removeClass(options.prefixClass + '-with-' + options.animation);\n          }\n        }\n\n        $modal.toggle = function() {\n\n          $modal.$isShown ? $modal.hide() : $modal.show();\n\n        };\n\n        $modal.focus = function() {\n          modalElement[0].focus();\n        };\n\n        // Protected methods\n\n        $modal.$onKeyUp = function(evt) {\n\n          if (evt.which === 27 && $modal.$isShown) {\n            $modal.hide();\n            evt.stopPropagation();\n          }\n\n        };\n\n        // Private methods\n\n        function hideOnBackdropClick(evt) {\n          if(evt.target !== evt.currentTarget) return;\n          options.backdrop === 'static' ? $modal.focus() : $modal.hide();\n        }\n\n        function preventEventDefault(evt) {\n          evt.preventDefault();\n        }\n\n        return $modal;\n\n      }\n\n      // Helper functions\n\n      function safeDigest(scope) {\n        scope.$$phase || (scope.$root && scope.$root.$$phase) || scope.$digest();\n      }\n\n      function findElement(query, element) {\n        return angular.element((element || document).querySelectorAll(query));\n      }\n\n      var fetchPromises = {};\n      function fetchTemplate(template) {\n        if(fetchPromises[template]) return fetchPromises[template];\n        return (fetchPromises[template] = $q.when($templateCache.get(template) || $http.get(template))\n        .then(function(res) {\n          if(angular.isObject(res)) {\n            $templateCache.put(template, res.data);\n            return res.data;\n          }\n          return res;\n        }));\n      }\n\n      return ModalFactory;\n\n    };\n\n  })\n\n  .directive('bsModal', function($window, $sce, $modal) {\n\n    return {\n      restrict: 'EAC',\n      scope: true,\n      link: function postLink(scope, element, attr, transclusion) {\n\n        // Directive options\n        var options = {scope: scope, element: element, show: false};\n        angular.forEach(['template', 'contentTemplate', 'placement', 'backdrop', 'keyboard', 'html', 'container', 'animation', 'id'], function(key) {\n          if(angular.isDefined(attr[key])) options[key] = attr[key];\n        });\n\n        // Support scope as data-attrs\n        angular.forEach(['title', 'content'], function(key) {\n          attr[key] && attr.$observe(key, function(newValue, oldValue) {\n            scope[key] = $sce.trustAsHtml(newValue);\n          });\n        });\n\n        // Support scope as an object\n        attr.bsModal && scope.$watch(attr.bsModal, function(newValue, oldValue) {\n          if(angular.isObject(newValue)) {\n            angular.extend(scope, newValue);\n          } else {\n            scope.content = newValue;\n          }\n        }, true);\n\n        // Initialize modal\n        var modal = $modal(options);\n\n        // Trigger\n        element.on(attr.trigger || 'click', modal.toggle);\n\n        // Garbage collection\n        scope.$on('$destroy', function() {\n          if (modal) modal.destroy();\n          options = null;\n          modal = null;\n        });\n\n      }\n    };\n\n  });\n"], "sourceRoot": "/source/"}