/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.timepicker",["mgcrea.ngStrap.helpers.dateParser","mgcrea.ngStrap.helpers.dateFormatter","mgcrea.ngStrap.tooltip"]).provider("$timepicker",function(){var e=this.defaults={animation:"am-fade",prefixClass:"timepicker",placement:"bottom-left",template:"timepicker/timepicker.tpl.html",trigger:"focus",container:!1,keyboard:!0,html:!1,delay:0,useNative:!0,timeType:"date",timeFormat:"shortTime",modelTimeFormat:null,autoclose:!1,minTime:-1/0,maxTime:+1/0,length:5,hourStep:1,minuteStep:5,iconUp:"glyphicon glyphicon-chevron-up",iconDown:"glyphicon glyphicon-chevron-down",arrowBehavior:"pager"};this.$get=["$window","$document","$rootScope","$sce","$dateFormatter","$tooltip","$timeout",function(t,a,n,i,o,r,u){function s(t,a,n){function i(e,a){if(t[0].createTextRange){var n=t[0].createTextRange();n.collapse(!0),n.moveStart("character",e),n.moveEnd("character",a),n.select()}else t[0].setSelectionRange?t[0].setSelectionRange(e,a):angular.isUndefined(t[0].selectionStart)&&(t[0].selectionStart=e,t[0].selectionEnd=a)}function s(){t[0].focus()}var m=r(t,angular.extend({},e,n)),c=n.scope,g=m.$options,p=m.$scope,$=g.lang,f=function(e,t){return o.formatDate(e,t,$)},h=0,w=a.$dateValue||new Date,v={hour:w.getHours(),meridian:w.getHours()<12,minute:w.getMinutes(),second:w.getSeconds(),millisecond:w.getMilliseconds()},y=o.getDatetimeFormat(g.timeFormat,$),T=o.hoursFormat(y),D=o.timeSeparator(y),S=o.minutesFormat(y),V=o.showAM(y);p.$iconUp=g.iconUp,p.$iconDown=g.iconDown,p.$select=function(e,t){m.select(e,t)},p.$moveIndex=function(e,t){m.$moveIndex(e,t)},p.$switchMeridian=function(e){m.switchMeridian(e)},m.update=function(e){angular.isDate(e)&&!isNaN(e.getTime())?(m.$date=e,angular.extend(v,{hour:e.getHours(),minute:e.getMinutes(),second:e.getSeconds(),millisecond:e.getMilliseconds()}),m.$build()):m.$isBuilt||m.$build()},m.select=function(e,t,n){(!a.$dateValue||isNaN(a.$dateValue.getTime()))&&(a.$dateValue=new Date(1970,0,1)),angular.isDate(e)||(e=new Date(e)),0===t?a.$dateValue.setHours(e.getHours()):1===t&&a.$dateValue.setMinutes(e.getMinutes()),a.$setViewValue(angular.copy(a.$dateValue)),a.$render(),g.autoclose&&!n&&u(function(){m.hide(!0)})},m.switchMeridian=function(e){if(a.$dateValue&&!isNaN(a.$dateValue.getTime())){var t=(e||a.$dateValue).getHours();a.$dateValue.setHours(12>t?t+12:t-12),a.$setViewValue(angular.copy(a.$dateValue)),a.$render()}},m.$build=function(){var e,t,a=p.midIndex=parseInt(g.length/2,10),n=[];for(e=0;e<g.length;e++)t=new Date(1970,0,1,v.hour-(a-e)*g.hourStep),n.push({date:t,label:f(t,T),selected:m.$date&&m.$isSelected(t,0),disabled:m.$isDisabled(t,0)});var i,o=[];for(e=0;e<g.length;e++)i=new Date(1970,0,1,0,v.minute-(a-e)*g.minuteStep),o.push({date:i,label:f(i,S),selected:m.$date&&m.$isSelected(i,1),disabled:m.$isDisabled(i,1)});var r=[];for(e=0;e<g.length;e++)r.push([n[e],o[e]]);p.rows=r,p.showAM=V,p.isAM=(m.$date||n[a].date).getHours()<12,p.timeSeparator=D,m.$isBuilt=!0},m.$isSelected=function(e,t){return m.$date?0===t?e.getHours()===m.$date.getHours():1===t?e.getMinutes()===m.$date.getMinutes():void 0:!1},m.$isDisabled=function(e,t){var a;return 0===t?a=e.getTime()+6e4*v.minute:1===t&&(a=e.getTime()+36e5*v.hour),a<1*g.minTime||a>1*g.maxTime},p.$arrowAction=function(e,t){"picker"===g.arrowBehavior?m.$setTimeByStep(e,t):m.$moveIndex(e,t)},m.$setTimeByStep=function(e,t){{var a=new Date(m.$date),n=a.getHours(),i=(f(a,T).length,a.getMinutes());f(a,S).length}0===t?a.setHours(n-parseInt(g.hourStep,10)*e):a.setMinutes(i-parseInt(g.minuteStep,10)*e),m.select(a,t,!0)},m.$moveIndex=function(e,t){var a;0===t?(a=new Date(1970,0,1,v.hour+e*g.length,v.minute),angular.extend(v,{hour:a.getHours()})):1===t&&(a=new Date(1970,0,1,v.hour,v.minute+e*g.length*g.minuteStep),angular.extend(v,{minute:a.getMinutes()})),m.$build()},m.$onMouseDown=function(e){if("input"!==e.target.nodeName.toLowerCase()&&e.preventDefault(),e.stopPropagation(),d){var t=angular.element(e.target);"button"!==t[0].nodeName.toLowerCase()&&(t=t.parent()),t.triggerHandler("click")}},m.$onKeyDown=function(e){if(/(38|37|39|40|13)/.test(e.keyCode)&&!e.shiftKey&&!e.altKey){if(e.preventDefault(),e.stopPropagation(),13===e.keyCode)return m.hide(!0);var t=new Date(m.$date),a=t.getHours(),n=f(t,T).length,o=t.getMinutes(),r=f(t,S).length,u=/(37|39)/.test(e.keyCode),s=2+1*V;u&&(37===e.keyCode?h=1>h?s-1:h-1:39===e.keyCode&&(h=s-1>h?h+1:0));var l=[0,n];0===h?(38===e.keyCode?t.setHours(a-parseInt(g.hourStep,10)):40===e.keyCode&&t.setHours(a+parseInt(g.hourStep,10)),n=f(t,T).length,l=[0,n]):1===h?(38===e.keyCode?t.setMinutes(o-parseInt(g.minuteStep,10)):40===e.keyCode&&t.setMinutes(o+parseInt(g.minuteStep,10)),r=f(t,S).length,l=[n+1,n+1+r]):2===h&&(u||m.switchMeridian(),l=[n+1+r+1,n+1+r+3]),m.select(t,h,!0),i(l[0],l[1]),c.$digest()}};var k=m.init;m.init=function(){return l&&g.useNative?(t.prop("type","time"),void t.css("-webkit-appearance","textfield")):(d&&(t.prop("type","text"),t.attr("readonly","true"),t.on("click",s)),void k())};var b=m.destroy;m.destroy=function(){l&&g.useNative&&t.off("click",s),b()};var M=m.show;m.show=function(){M(),u(function(){m.$element.on(d?"touchstart":"mousedown",m.$onMouseDown),g.keyboard&&t.on("keydown",m.$onKeyDown)},0,!1)};var N=m.hide;return m.hide=function(e){m.$isShown&&(m.$element.off(d?"touchstart":"mousedown",m.$onMouseDown),g.keyboard&&t.off("keydown",m.$onKeyDown),N(e))},m}var l=(angular.element(t.document.body),/(ip(a|o)d|iphone|android)/gi.test(t.navigator.userAgent)),d="createTouch"in t.document&&l;return e.lang||(e.lang=o.getDefaultLocale()),s.defaults=e,s}]}).directive("bsTimepicker",["$window","$parse","$q","$dateFormatter","$dateParser","$timepicker",function(e,t,a,n,i,o){{var r=o.defaults,u=/(ip(a|o)d|iphone|android)/gi.test(e.navigator.userAgent);e.requestAnimationFrame||e.setTimeout}return{restrict:"EAC",require:"ngModel",link:function(e,t,a,s){function l(e){if(angular.isDate(e)){var t=isNaN(m.minTime)||new Date(e.getTime()).setFullYear(1970,0,1)>=m.minTime,a=isNaN(m.maxTime)||new Date(e.getTime()).setFullYear(1970,0,1)<=m.maxTime,n=t&&a;s.$setValidity("date",n),s.$setValidity("min",t),s.$setValidity("max",a),n&&(s.$dateValue=e)}}function d(){return!s.$dateValue||isNaN(s.$dateValue.getTime())?"":p(s.$dateValue,m.timeFormat)}var m={scope:e,controller:s};angular.forEach(["placement","container","delay","trigger","keyboard","html","animation","template","autoclose","timeType","timeFormat","modelTimeFormat","useNative","hourStep","minuteStep","length","arrowBehavior","iconUp","iconDown","id"],function(e){angular.isDefined(a[e])&&(m[e]=a[e])}),a.bsShow&&e.$watch(a.bsShow,function(e){c&&angular.isDefined(e)&&(angular.isString(e)&&(e=!!e.match(/true|,?(timepicker),?/i)),e===!0?c.show():c.hide())}),u&&(m.useNative||r.useNative)&&(m.timeFormat="HH:mm");var c=o(t,s,m);m=c.$options;var g=m.lang,p=function(e,t){return n.formatDate(e,t,g)},$=i({format:m.timeFormat,lang:g});angular.forEach(["minTime","maxTime"],function(e){angular.isDefined(a[e])&&a.$observe(e,function(t){c.$options[e]=$.getTimeForAttribute(e,t),!isNaN(c.$options[e])&&c.$build(),l(s.$dateValue)})}),e.$watch(a.ngModel,function(){c.update(s.$dateValue)},!0),s.$parsers.unshift(function(e){if(!e)return s.$setValidity("date",!0),null;var t=angular.isDate(e)?e:$.parse(e,s.$dateValue);return!t||isNaN(t.getTime())?void s.$setValidity("date",!1):(l(t),"string"===m.timeType?p(t,m.modelTimeFormat||m.timeFormat):"number"===m.timeType?s.$dateValue.getTime():"unix"===m.timeType?s.$dateValue.getTime()/1e3:"iso"===m.timeType?s.$dateValue.toISOString():new Date(s.$dateValue))}),s.$formatters.push(function(e){var t;return t=angular.isUndefined(e)||null===e?0/0:angular.isDate(e)?e:"string"===m.timeType?$.parse(e,null,m.modelTimeFormat):new Date("unix"===m.timeType?1e3*e:e),s.$dateValue=t,d()}),s.$render=function(){t.val(d())},e.$on("$destroy",function(){c&&c.destroy(),m=null,c=null})}}}]);
//# sourceMappingURL=timepicker.min.js.map