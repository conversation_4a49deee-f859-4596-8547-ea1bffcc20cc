{"version": 3, "sources": ["timepicker/timepicker.js"], "names": [], "mappings": "qBASQ,OAAA,4IAKF,cAAS,cAET,GAAU,KAAA,UACV,UAAM,UACN,YAAO,qCAEP,SAAA,iCACA,QAAA,QACA,WAAA,EACA,UAAA,EACA,MAAA,EACA,MAAA,EAEA,WAAQ,EACR,SAAU,OACV,WAAY,YACZ,gBAAQ,KACR,WAAU,EACV,SAAA,0BAGF,SAAK,eAEH,OAAI,iCACJ,SAAI,mCACJ,cAAe,cAGf,MAAS,UAAA,YAAkB,aAAqB,OAAQ,iBAAA,WAAA,WAAA,SAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,WAOlD,GAAe,EAAA,EAAA,WAuNV,GAAc,EAAA,MACnB,EAAQ,GAAG,gBAAkB,OACrB,EAAQ,GAAA,iBAChB,GAAQ,UAAG,GACX,EAAQ,UAAG,YAAe,mEAI9B,EAAS,GAAA,kBAAe,EAAA,GACd,QAAG,YAAA,EAAA,GAAA,6DAMb,QAAA,KACE,EAAG,GAAA,WArOH,GAAO,EAAe,EAAW,QAAM,UAAQ,EAAA,sCAK7C,EAAA,EAAA,KACA,EAAY,SAAA,EAAW,GAC3B,MAAI,GAAkB,WAAU,EAAA,EAAY,IAK1C,EAAgB,EAChB,EAAA,EAAgB,YAAe,GAAA,MAC/B,GAAS,KAAA,EAAe,WAAO,SAAA,EAAA,WAAA,GAAA,OAAA,EAAA,aAAA,OAAA,EAAA,aAAA,YAAA,EAAA,mBAEjC,EAAM,EAAkB,kBAAA,EAAA,WAAA,gEAKxB,EAAM,EAAmB,OAAM,sBAG/B,EAAM,UAAA,EAAa,WAIjB,QAAY,SAAA,EAAe,8FAO3B,EAAW,eAAgB,MAKzB,OAAY,SAAA,qDAIhB,QAAY,OAAA,GAAkB,KAAM,EAAA,WAAa,OAAA,EAAA,aAAA,OAAA,EAAA,aAAA,YAAA,EAAA,gCAE3C,EAAW,UACf,EAAY,YAIZ,OAAW,SAAA,EAAA,EAAA,KAET,EAAS,YAAa,MAAA,EAAiB,WAAA,cAAA,EAAA,WAAA,GAAA,MAAA,KAAA,EAAA,gIAI3C,EAAY,cAAA,QAAiB,KAAS,EAAM,aAC1C,EAAK,UACH,EAAA,YAAA,gCAKF,eAAW,SAAA,sFAKb,GAAY,WAAS,SAAW,GAAA,EAAA,EAAA,GAAA,EAAA,gDAE9B,EAAO,gCAOP,GAAI,GACW,EADX,EAAW,EAAQ,SAAQ,SAAK,EAAA,OAAA,EAAA,IAClC,SACA,EAAA,EAAQ,EAAK,EAAO,OAAQ,+IAI9B,IAAe,GAAX,SACF,EAAK,EAAA,EAAM,EAAU,OAAQ,uDAE/B,EAAM,MAAO,KAAA,EAAA,MAAA,EAAA,EAAA,GAAA,SAAA,EAAA,OAAA,EAAA,YAAA,EAAA,GAAA,SAAA,EAAA,YAAA,EAAA,IAGb,IAAA,KACA,KAAA,EAAA,EAAA,EAAY,EAAW,OAAA,uBAGzB,GAAA,KAAY,EACV,EAAI,OAAA,IACC,MAAG,EAAa,OAAA,EAAA,GAAA,MAAA,WAAA,KACnB,cAAY,IACJ,UAAa,8CAKb,IAAZ,EACM,EAAA,aAAA,EAAA,MAAA,WACY,IAAb,EACD,EAAA,eAAoB,EAAqB,MAAA,aADxC,aAKI,YAAe,SAAQ,EAAA,sBAGhC,EAAM,EAAe,UAAwB,IAAd,EAAc,OAC/B,IAAR,IACF,EAAY,EAAA,UAAqB,KAAN,EAAM,MAEjC,EAA6B,EAAjB,EAAW,SAAM,EAAA,EAAA,EAAA,sCAIJ,WAA7B,EAAY,cACV,EAAc,eAAS,EAAY,GAEnC,EAAc,WAAQ,EAAA,MAIjB,eAAA,SAAA,EAAA,OACH,GAAQ,GAAA,MAAW,EAAW,sBAEhC,iBAAY,EAAO,aAAgB,GAAA,EAAA,GAAA,qDAKnC,EAAG,WAAa,EAAA,SAAA,EAAA,WAAA,IAAA,KAEN,OAAO,EAAW,GAAM,MAGxB,WAAO,SAAW,EAAQ,QAExB,KAAZ,0FAGU,IAAA,iEAEV,QAAO,OAAO,GAAS,OAAA,EAAkB,8BAKpC,aAAY,SAAS,sEAGxB,oDAImC,YAAvC,EAAY,GAAA,SAAa,gBACnB,EAAC,EAAmB,UAExB,EAAI,eAAA,mGAUJ,GAJA,EAAI,iBACJ,EAAI,kBAGY,KAAhB,EAAI,QAAY,MAAS,GAAA,MAAA,EAGzB,IAAI,GAAA,GAAa,MAAA,EAAA,OACf,EAAO,EAAA,WAAgB,EAAgB,EAAgB,EAAI,GAAY,SAC3D,EAAA,aAAgB,EAAgB,EAAgB,EAAQ,GAAI,0CAKvE,KACkB,KAAhB,EAAI,QAAgB,EAAyB,EAAjB,EAA0B,EAAQ,EAAA,EAAU,EACnD,KAAhB,EAAI,UAAgB,EAAyB,EAAS,EAA1B,EAAkC,EAAU,EAAA,WAIxE,EAAA,EACW,KAAnB,GACY,OAAP,QAAO,EAAgB,SAAQ,EAAW,SAAU,EAAA,SAAiB,2DAG1E,EAAc,EAAC,EAAiB,GAAc,UACtC,EAAA,IACS,IAAb,GACW,KAAf,EAAA,QAAe,EAAc,WAAI,EAAmB,SAAA,EAAc,WAAI,+DAGxE,EAAgB,EAAY,EAAI,GAAY,OAC5C,GAAY,EAAA,EAAA,EAAA,EAAA,uDAKd,EAAS,OAAA,EAAgB,GAAY,GACnC,EAAc,EAAA,GAAiB,EAAA,MACzB,eA0BJ,GAAQ,EAAK,OACL,KAAK,iBACb,IAAW,EAAS,qCAEtB,GAAA,IAAA,qBAAA,mBAGE,EAAA,KAAW,OAAA,QACf,EAAY,KAAA,WAAU,QACpB,EAAG,GAAA,QAAY,8BAMjB,GAAY,QAAA,WACZ,GAAY,EAAO,WACjB,EAAA,IAAA,QAAA,WAKE,GAAG,EAAQ,OACT,KAAW,4BAKb,EAAQ,SAAY,GAAA,EAAA,aAAA,YAAA,EAAA,cACxB,EAAY,UACN,EAAA,GAAA,UAAsB,EAAA,aAEvB,GAAA,OAGH,GAAM,EAAA,4CAGR,EAAO,SAAA,IAAA,EAAA,aAAA,YAAA,EAAA,wDAIT,EAAA,UA1RE,IADI,QAAc,QAAS,EAAA,SAAiB,MACxC,8BAAqB,KAAA,EAAA,UAAA,YACzB,EAAI,eAAsB,GAAA,UAAA,CAiS/B,OAhSK,GAAI,OAAQ,EAAY,KAAA,EAAA,iCAgSnB,gBAOP,gBAAU,UAAA,SAAA,KAAA,iBAAA,cAAA,cAAA,SAAA,EAAA,EAAA,EAAA,EAAA,EAAA,OAEV,GAAM,EAAkB,2HAKjB,uDA8CH,GAA+B,yBAE/B,GAAI,GAAS,MAAA,EAAA,UAAA,GAAA,MAAA,EAAA,WAAA,YAAA,KAAA,EAAA,IAAA,EAAA,QACT,EAAA,MAAA,EAAA,UAAA,GAAA,MAAA,EAAA,WAAA,YAAA,KAAA,EAAA,IAAA,EAAA,cAEJ,GAAW,aAAa,OAAA,mDAI1B,IAGE,EAAI,WAAW,YA+Df,KACA,OAAA,EAAa,YAAA,MAAA,EAAA,WAAA,WAAA,GAAA,EAAA,EAAA,WAAA,EAAA,eArHb,IAAI,MAAe,EAAQ,WAAU,WAClC,SAAQ,YAAS,YAAW,QAAa,UAAe,WAAA,OAAA,YAAA,WAAA,YAAA,WAAA,aAAA,kBAAA,YAAA,WAAA,aAAA,SAAA,gBAAA,SAAA,WAAA,MAAA,SAAA,GAC3D,QAAA,UAAoB,EAAA,MAAW,EAAS,GAAA,EAAW,MAIrD,EAAG,QAAA,EAAa,OAAQ,EAAA,OAAa,SAAS,GAC1C,GAAa,QAAY,UAAS,KACtC,QAAU,SAAW,KAAA,IAAA,EAAA,MAAA,sKAWrB,MAAQ,GAAS,WAAW,EAAY,EAAS,IAI7C,EAAO,GAAW,OAAS,EAAS,WAAW,KAAA,wGAMnD,EAAa,SAAK,GAAS,EAAS,oBAAoB,EAAA,qCAEtD,EAAkB,EAAW,kBAKzB,OAAA,EAAA,QAAmB,WAEvB,EAAI,OAAU,EAAc,cAC5B,KAkBE,SAAW,QAAa,SAAQ,GAGlC,IAAI,EAKF,iCAAA,QAEA,GAAA,QAAA,OAA0B,GAAA,EAAA,EAAA,MAAA,EAAA,EAAA,6CAE5B,GAAW,aAAa,QAAU,IAKhC,EAAkB,GAEA,WAAlB,EAAO,SACF,EAAA,EAAA,EAAA,iBAAA,EAAA,YACsB,WAApB,EAAS,kGAKT,EAAA,WAAiB,cAEtB,GAAA,MAAA,EAAA,iBAKG,YAAW,KAAA,SAAa,kBAG7B,QAAO,YAAS,IAAa,OAAA,EACxB,IACE,QAAS,OAAA,6DAKX,GAAA,MADiB,SAAxB,EAAW,SACJ,IAAA,KAMP,EAAQ,WAAI,oFAiBjB,EAAA", "file": "timepicker.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.timepicker', [\n  'mgcrea.ngStrap.helpers.dateParser',\n  'mgcrea.ngStrap.helpers.dateFormatter',\n  'mgcrea.ngStrap.tooltip'])\n\n  .provider('$timepicker', function() {\n\n    var defaults = this.defaults = {\n      animation: 'am-fade',\n      prefixClass: 'timepicker',\n      placement: 'bottom-left',\n      template: 'timepicker/timepicker.tpl.html',\n      trigger: 'focus',\n      container: false,\n      keyboard: true,\n      html: false,\n      delay: 0,\n      // lang: $locale.id,\n      useNative: true,\n      timeType: 'date',\n      timeFormat: 'shortTime',\n      modelTimeFormat: null,\n      autoclose: false,\n      minTime: -Infinity,\n      maxTime: +Infinity,\n      length: 5,\n      hourStep: 1,\n      minuteStep: 5,\n      iconUp: 'glyphicon glyphicon-chevron-up',\n      iconDown: 'glyphicon glyphicon-chevron-down',\n      arrowBehavior: 'pager'\n    };\n\n    this.$get = function($window, $document, $rootScope, $sce, $dateFormatter, $tooltip, $timeout) {\n\n      var bodyEl = angular.element($window.document.body);\n      var isNative = /(ip(a|o)d|iphone|android)/ig.test($window.navigator.userAgent);\n      var isTouch = ('createTouch' in $window.document) && isNative;\n      if(!defaults.lang) defaults.lang = $dateFormatter.getDefaultLocale();\n\n      function timepickerFactory(element, controller, config) {\n\n        var $timepicker = $tooltip(element, angular.extend({}, defaults, config));\n        var parentScope = config.scope;\n        var options = $timepicker.$options;\n        var scope = $timepicker.$scope;\n\n        var lang = options.lang;\n        var formatDate = function(date, format) {\n          return $dateFormatter.formatDate(date, format, lang);\n        };\n\n        // View vars\n\n        var selectedIndex = 0;\n        var startDate = controller.$dateValue || new Date();\n        var viewDate = {hour: startDate.getHours(), meridian: startDate.getHours() < 12, minute: startDate.getMinutes(), second: startDate.getSeconds(), millisecond: startDate.getMilliseconds()};\n\n        var format = $dateFormatter.getDatetimeFormat(options.timeFormat, lang);\n\n        var hoursFormat = $dateFormatter.hoursFormat(format),\n          timeSeparator = $dateFormatter.timeSeparator(format),\n          minutesFormat = $dateFormatter.minutesFormat(format),\n          showAM = $dateFormatter.showAM(format);\n\n        scope.$iconUp = options.iconUp;\n        scope.$iconDown = options.iconDown;\n\n        // Scope methods\n\n        scope.$select = function(date, index) {\n          $timepicker.select(date, index);\n        };\n        scope.$moveIndex = function(value, index) {\n          $timepicker.$moveIndex(value, index);\n        };\n        scope.$switchMeridian = function(date) {\n          $timepicker.switchMeridian(date);\n        };\n\n        // Public methods\n\n        $timepicker.update = function(date) {\n          // console.warn('$timepicker.update() newValue=%o', date);\n          if(angular.isDate(date) && !isNaN(date.getTime())) {\n            $timepicker.$date = date;\n            angular.extend(viewDate, {hour: date.getHours(), minute: date.getMinutes(), second: date.getSeconds(), millisecond: date.getMilliseconds()});\n            $timepicker.$build();\n          } else if(!$timepicker.$isBuilt) {\n            $timepicker.$build();\n          }\n        };\n\n        $timepicker.select = function(date, index, keep) {\n          // console.warn('$timepicker.select', date, scope.$mode);\n          if(!controller.$dateValue || isNaN(controller.$dateValue.getTime())) controller.$dateValue = new Date(1970, 0, 1);\n          if(!angular.isDate(date)) date = new Date(date);\n          if(index === 0) controller.$dateValue.setHours(date.getHours());\n          else if(index === 1) controller.$dateValue.setMinutes(date.getMinutes());\n          controller.$setViewValue(angular.copy(controller.$dateValue));\n          controller.$render();\n          if(options.autoclose && !keep) {\n            $timeout(function() { $timepicker.hide(true); });\n          }\n        };\n\n        $timepicker.switchMeridian = function(date) {\n          if (!controller.$dateValue || isNaN(controller.$dateValue.getTime())) {\n            return;\n          }\n          var hours = (date || controller.$dateValue).getHours();\n          controller.$dateValue.setHours(hours < 12 ? hours + 12 : hours - 12);\n          controller.$setViewValue(angular.copy(controller.$dateValue));\n          controller.$render();\n        };\n\n        // Protected methods\n\n        $timepicker.$build = function() {\n          // console.warn('$timepicker.$build() viewDate=%o', viewDate);\n          var i, midIndex = scope.midIndex = parseInt(options.length / 2, 10);\n          var hours = [], hour;\n          for(i = 0; i < options.length; i++) {\n            hour = new Date(1970, 0, 1, viewDate.hour - (midIndex - i) * options.hourStep);\n            hours.push({date: hour, label: formatDate(hour, hoursFormat), selected: $timepicker.$date && $timepicker.$isSelected(hour, 0), disabled: $timepicker.$isDisabled(hour, 0)});\n          }\n          var minutes = [], minute;\n          for(i = 0; i < options.length; i++) {\n            minute = new Date(1970, 0, 1, 0, viewDate.minute - (midIndex - i) * options.minuteStep);\n            minutes.push({date: minute, label: formatDate(minute, minutesFormat), selected: $timepicker.$date && $timepicker.$isSelected(minute, 1), disabled: $timepicker.$isDisabled(minute, 1)});\n          }\n\n          var rows = [];\n          for(i = 0; i < options.length; i++) {\n            rows.push([hours[i], minutes[i]]);\n          }\n          scope.rows = rows;\n          scope.showAM = showAM;\n          scope.isAM = ($timepicker.$date || hours[midIndex].date).getHours() < 12;\n          scope.timeSeparator = timeSeparator;\n          $timepicker.$isBuilt = true;\n        };\n\n        $timepicker.$isSelected = function(date, index) {\n          if(!$timepicker.$date) return false;\n          else if(index === 0) {\n            return date.getHours() === $timepicker.$date.getHours();\n          } else if(index === 1) {\n            return date.getMinutes() === $timepicker.$date.getMinutes();\n          }\n        };\n\n        $timepicker.$isDisabled = function(date, index) {\n          var selectedTime;\n          if(index === 0) {\n            selectedTime = date.getTime() + viewDate.minute * 6e4;\n          } else if(index === 1) {\n            selectedTime = date.getTime() + viewDate.hour * 36e5;\n          }\n          return selectedTime < options.minTime * 1 || selectedTime > options.maxTime * 1;\n        };\n\n        scope.$arrowAction = function (value, index) {\n          if (options.arrowBehavior === 'picker') {\n            $timepicker.$setTimeByStep(value,index);\n          } else {\n            $timepicker.$moveIndex(value,index);\n          }\n        };\n\n        $timepicker.$setTimeByStep = function(value, index) {\n          var newDate = new Date($timepicker.$date);\n          var hours = newDate.getHours(), hoursLength = formatDate(newDate, hoursFormat).length;\n          var minutes = newDate.getMinutes(), minutesLength = formatDate(newDate, minutesFormat).length;\n          if (index === 0) {\n            newDate.setHours(hours - (parseInt(options.hourStep, 10) * value));\n          }\n          else {\n            newDate.setMinutes(minutes - (parseInt(options.minuteStep, 10) * value));\n          }\n          $timepicker.select(newDate, index, true);\n        };\n\n        $timepicker.$moveIndex = function(value, index) {\n          var targetDate;\n          if(index === 0) {\n            targetDate = new Date(1970, 0, 1, viewDate.hour + (value * options.length), viewDate.minute);\n            angular.extend(viewDate, {hour: targetDate.getHours()});\n          } else if(index === 1) {\n            targetDate = new Date(1970, 0, 1, viewDate.hour, viewDate.minute + (value * options.length * options.minuteStep));\n            angular.extend(viewDate, {minute: targetDate.getMinutes()});\n          }\n          $timepicker.$build();\n        };\n\n        $timepicker.$onMouseDown = function(evt) {\n          // Prevent blur on mousedown on .dropdown-menu\n          if(evt.target.nodeName.toLowerCase() !== 'input') evt.preventDefault();\n          evt.stopPropagation();\n          // Emulate click for mobile devices\n          if(isTouch) {\n            var targetEl = angular.element(evt.target);\n            if(targetEl[0].nodeName.toLowerCase() !== 'button') {\n              targetEl = targetEl.parent();\n            }\n            targetEl.triggerHandler('click');\n          }\n        };\n\n        $timepicker.$onKeyDown = function(evt) {\n          if (!/(38|37|39|40|13)/.test(evt.keyCode) || evt.shiftKey || evt.altKey) return;\n          evt.preventDefault();\n          evt.stopPropagation();\n\n          // Close on enter\n          if(evt.keyCode === 13) return $timepicker.hide(true);\n\n          // Navigate with keyboard\n          var newDate = new Date($timepicker.$date);\n          var hours = newDate.getHours(), hoursLength = formatDate(newDate, hoursFormat).length;\n          var minutes = newDate.getMinutes(), minutesLength = formatDate(newDate, minutesFormat).length;\n          var lateralMove = /(37|39)/.test(evt.keyCode);\n          var count = 2 + showAM * 1;\n\n          // Navigate indexes (left, right)\n          if (lateralMove) {\n            if(evt.keyCode === 37) selectedIndex = selectedIndex < 1 ? count - 1 : selectedIndex - 1;\n            else if(evt.keyCode === 39) selectedIndex = selectedIndex < count - 1 ? selectedIndex + 1 : 0;\n          }\n\n          // Update values (up, down)\n          var selectRange = [0, hoursLength];\n          if(selectedIndex === 0) {\n            if(evt.keyCode === 38) newDate.setHours(hours - parseInt(options.hourStep, 10));\n            else if(evt.keyCode === 40) newDate.setHours(hours + parseInt(options.hourStep, 10));\n            // re-calculate hours length because we have changed hours value\n            hoursLength = formatDate(newDate, hoursFormat).length;\n            selectRange = [0, hoursLength];\n          } else if(selectedIndex === 1) {\n            if(evt.keyCode === 38) newDate.setMinutes(minutes - parseInt(options.minuteStep, 10));\n            else if(evt.keyCode === 40) newDate.setMinutes(minutes + parseInt(options.minuteStep, 10));\n            // re-calculate minutes length because we have changes minutes value\n            minutesLength = formatDate(newDate, minutesFormat).length;\n            selectRange = [hoursLength + 1, hoursLength + 1 + minutesLength];\n          } else if(selectedIndex === 2) {\n            if(!lateralMove) $timepicker.switchMeridian();\n            selectRange = [hoursLength + 1 + minutesLength + 1, hoursLength + 1 + minutesLength + 3];\n          }\n          $timepicker.select(newDate, selectedIndex, true);\n          createSelection(selectRange[0], selectRange[1]);\n          parentScope.$digest();\n        };\n\n        // Private\n\n        function createSelection(start, end) {\n          if(element[0].createTextRange) {\n            var selRange = element[0].createTextRange();\n            selRange.collapse(true);\n            selRange.moveStart('character', start);\n            selRange.moveEnd('character', end);\n            selRange.select();\n          } else if(element[0].setSelectionRange) {\n            element[0].setSelectionRange(start, end);\n          } else if(angular.isUndefined(element[0].selectionStart)) {\n            element[0].selectionStart = start;\n            element[0].selectionEnd = end;\n          }\n        }\n\n        function focusElement() {\n          element[0].focus();\n        }\n\n        // Overrides\n\n        var _init = $timepicker.init;\n        $timepicker.init = function() {\n          if(isNative && options.useNative) {\n            element.prop('type', 'time');\n            element.css('-webkit-appearance', 'textfield');\n            return;\n          } else if(isTouch) {\n            element.prop('type', 'text');\n            element.attr('readonly', 'true');\n            element.on('click', focusElement);\n          }\n          _init();\n        };\n\n        var _destroy = $timepicker.destroy;\n        $timepicker.destroy = function() {\n          if(isNative && options.useNative) {\n            element.off('click', focusElement);\n          }\n          _destroy();\n        };\n\n        var _show = $timepicker.show;\n        $timepicker.show = function() {\n          _show();\n          // use timeout to hookup the events to prevent\n          // event bubbling from being processed imediately.\n          $timeout(function() {\n            $timepicker.$element.on(isTouch ? 'touchstart' : 'mousedown', $timepicker.$onMouseDown);\n            if(options.keyboard) {\n              element.on('keydown', $timepicker.$onKeyDown);\n            }\n          }, 0, false);\n        };\n\n        var _hide = $timepicker.hide;\n        $timepicker.hide = function(blur) {\n          if(!$timepicker.$isShown) return;\n          $timepicker.$element.off(isTouch ? 'touchstart' : 'mousedown', $timepicker.$onMouseDown);\n          if(options.keyboard) {\n            element.off('keydown', $timepicker.$onKeyDown);\n          }\n          _hide(blur);\n        };\n\n        return $timepicker;\n\n      }\n\n      timepickerFactory.defaults = defaults;\n      return timepickerFactory;\n\n    };\n\n  })\n\n\n  .directive('bsTimepicker', function($window, $parse, $q, $dateFormatter, $dateParser, $timepicker) {\n\n    var defaults = $timepicker.defaults;\n    var isNative = /(ip(a|o)d|iphone|android)/ig.test($window.navigator.userAgent);\n    var requestAnimationFrame = $window.requestAnimationFrame || $window.setTimeout;\n\n    return {\n      restrict: 'EAC',\n      require: 'ngModel',\n      link: function postLink(scope, element, attr, controller) {\n\n        // Directive options\n        var options = {scope: scope, controller: controller};\n        angular.forEach(['placement', 'container', 'delay', 'trigger', 'keyboard', 'html', 'animation', 'template', 'autoclose', 'timeType', 'timeFormat', 'modelTimeFormat', 'useNative', 'hourStep', 'minuteStep', 'length', 'arrowBehavior', 'iconUp', 'iconDown', 'id'], function(key) {\n          if(angular.isDefined(attr[key])) options[key] = attr[key];\n        });\n\n        // Visibility binding support\n        attr.bsShow && scope.$watch(attr.bsShow, function(newValue, oldValue) {\n          if(!timepicker || !angular.isDefined(newValue)) return;\n          if(angular.isString(newValue)) newValue = !!newValue.match(/true|,?(timepicker),?/i);\n          newValue === true ? timepicker.show() : timepicker.hide();\n        });\n\n        // Initialize timepicker\n        if(isNative && (options.useNative || defaults.useNative)) options.timeFormat = 'HH:mm';\n        var timepicker = $timepicker(element, controller, options);\n        options = timepicker.$options;\n\n        var lang = options.lang;\n        var formatDate = function(date, format) {\n          return $dateFormatter.formatDate(date, format, lang);\n        };\n\n        // Initialize parser\n        var dateParser = $dateParser({format: options.timeFormat, lang: lang});\n\n        // Observe attributes for changes\n        angular.forEach(['minTime', 'maxTime'], function(key) {\n          // console.warn('attr.$observe(%s)', key, attr[key]);\n          angular.isDefined(attr[key]) && attr.$observe(key, function(newValue) {\n            timepicker.$options[key] = dateParser.getTimeForAttribute(key, newValue);\n            !isNaN(timepicker.$options[key]) && timepicker.$build();\n            validateAgainstMinMaxTime(controller.$dateValue);\n          });\n        });\n\n        // Watch model for changes\n        scope.$watch(attr.ngModel, function(newValue, oldValue) {\n          // console.warn('scope.$watch(%s)', attr.ngModel, newValue, oldValue, controller.$dateValue);\n          timepicker.update(controller.$dateValue);\n        }, true);\n\n        function validateAgainstMinMaxTime(parsedTime) {\n          if (!angular.isDate(parsedTime)) return;\n          var isMinValid = isNaN(options.minTime) || new Date(parsedTime.getTime()).setFullYear(1970, 0, 1) >= options.minTime;\n          var isMaxValid = isNaN(options.maxTime) || new Date(parsedTime.getTime()).setFullYear(1970, 0, 1) <= options.maxTime;\n          var isValid = isMinValid && isMaxValid;\n          controller.$setValidity('date', isValid);\n          controller.$setValidity('min', isMinValid);\n          controller.$setValidity('max', isMaxValid);\n          // Only update the model when we have a valid date\n          if(!isValid) {\n              return;\n          }\n          controller.$dateValue = parsedTime;\n        }\n\n        // viewValue -> $parsers -> modelValue\n        controller.$parsers.unshift(function(viewValue) {\n          // console.warn('$parser(\"%s\"): viewValue=%o', element.attr('ng-model'), viewValue);\n          // Null values should correctly reset the model value & validity\n          if(!viewValue) {\n            // BREAKING CHANGE:\n            // return null (not undefined) when input value is empty, so angularjs 1.3\n            // ngModelController can go ahead and run validators, like ngRequired\n            controller.$setValidity('date', true);\n            return null;\n          }\n          var parsedTime = angular.isDate(viewValue) ? viewValue : dateParser.parse(viewValue, controller.$dateValue);\n          if(!parsedTime || isNaN(parsedTime.getTime())) {\n            controller.$setValidity('date', false);\n            // return undefined, causes ngModelController to\n            // invalidate model value\n            return;\n          } else {\n            validateAgainstMinMaxTime(parsedTime);\n          }\n          if(options.timeType === 'string') {\n            return formatDate(parsedTime, options.modelTimeFormat || options.timeFormat);\n          } else if(options.timeType === 'number') {\n            return controller.$dateValue.getTime();\n          } else if(options.timeType === 'unix') {\n            return controller.$dateValue.getTime() / 1000;\n          } else if(options.timeType === 'iso') {\n            return controller.$dateValue.toISOString();\n          } else {\n            return new Date(controller.$dateValue);\n          }\n        });\n\n        // modelValue -> $formatters -> viewValue\n        controller.$formatters.push(function(modelValue) {\n          // console.warn('$formatter(\"%s\"): modelValue=%o (%o)', element.attr('ng-model'), modelValue, typeof modelValue);\n          var date;\n          if(angular.isUndefined(modelValue) || modelValue === null) {\n            date = NaN;\n          } else if(angular.isDate(modelValue)) {\n            date = modelValue;\n          } else if(options.timeType === 'string') {\n            date = dateParser.parse(modelValue, null, options.modelTimeFormat);\n          } else if(options.timeType === 'unix') {\n            date = new Date(modelValue * 1000);\n          } else {\n            date = new Date(modelValue);\n          }\n          // Setup default value?\n          // if(isNaN(date.getTime())) date = new Date(new Date().setMinutes(0) + 36e5);\n          controller.$dateValue = date;\n          return getTimeFormattedString();\n        });\n\n        // viewValue -> element\n        controller.$render = function() {\n          // console.warn('$render(\"%s\"): viewValue=%o', element.attr('ng-model'), controller.$viewValue);\n          element.val(getTimeFormattedString());\n        };\n\n        function getTimeFormattedString() {\n          return !controller.$dateValue || isNaN(controller.$dateValue.getTime()) ? '' : formatDate(controller.$dateValue, options.timeFormat);\n        }\n\n        // Garbage collection\n        scope.$on('$destroy', function() {\n          if (timepicker) timepicker.destroy();\n          options = null;\n          timepicker = null;\n        });\n\n      }\n    };\n\n  });\n"], "sourceRoot": "/source/"}