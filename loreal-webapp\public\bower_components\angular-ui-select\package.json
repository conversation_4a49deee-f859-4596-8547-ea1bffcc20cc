{"name": "ui-select", "main": "dist/select.js", "author": "http://github.com/angular-ui/ui-select/graphs/contributors", "homepage": "http://github.com/angular-ui/ui-select", "repository": {"url": "git://github.com/angular-ui/ui-select.git"}, "version": "0.8.3", "devDependencies": {"bower": "~1.3", "del": "~0.1.1", "event-stream": "~3.1.0", "gulp": "~3.8.5", "gulp-angular-templatecache": "~1.2.1", "gulp-concat": "~2.1.7", "gulp-header": "~1.0.2", "gulp-jshint": "1.6.4", "gulp-minify-css": "~0.3.6", "gulp-minify-html": "~0.1.0", "gulp-plumber": "^0.6.3", "gulp-rename": "~0.2.2", "gulp-uglify": "~0.3.1", "gulp-util": "^2.2.19", "jshint-stylish": "~0.3.0", "karma": "^0.12.16", "karma-chrome-launcher": "^0.1.3", "karma-firefox-launcher": "~0.1", "karma-jasmine": "~0.2", "karma-ng-html2js-preprocessor": "^0.1.0", "karma-phantomjs-launcher": "~0.1.4", "karma-coverage": "~0.2"}, "scripts": {"postinstall": "bower install", "test": "gulp test"}, "license": "MIT"}