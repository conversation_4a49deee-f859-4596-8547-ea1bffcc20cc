{"version": 3, "sources": ["tab/tab.js"], "names": [], "mappings": "qBASM,OAAA,oDAIF,GAAI,GAAA,KAAa,UACf,UAAW,0DAGX,YAAK,sDAOL,GAAA,SAAO,QAAe,KAAK,sEAEtB,QAAA,UAAgB,EAAS,MAAA,EAAA,SAAA,GAAA,EAAA,sCAK9B,EAAK,aAAA,EAAA,SAA6B,YAElC,EAAK,OAAQ,EAAA,YAKX,2BAAgC,EAAA,gEAMhC,QAAY,SAAA,kDAKP,OAAI,OAAU,EAAA,OAKnB,gCAKA,MAEE,WAAA,gFAMN,EAAK,2BAAkB,QAAA,SAAA,GACjB,qCAUN,OAFD,GAAA,SAAU,iBAEL,eAKK,UAAA,UAAA,WAAA,OAAA,SAAA,SAAA,EAAA,EAAA,EAAA,MAEP,GAAa,EAAA,gBAGb,SAAM,WAAS,+BAET,cACA,SAAa,WAAY,SAAA,EAAA,+FAM3B,GAAa,EAAA,oGAUX,2BAAmC,KAAA,WACnC,EAAO,cAAA,EAAA,OAAA,wEAYP,EAAA,aAAmB,2BAMlB,2BAAA,KAAA,kGAOA,mBAOD,UAAA,UAAc,WAAY,OAAA,SAAA,EAAA,EAAA,iDAI9B,iCA2BE,+BAEF,EAAA,EAAA,OAAA,oEA1BA,GACE,IADa,EAAS,GACR,EAAK,2GAYrB,EAAU,SAAA,EAAY,SAAW,WAIjC,EAAS,MAAA,KAGP,IAAA,WAAmB,uEAWxB", "file": "tab.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.tab', [])\n\n  .provider('$tab', function() {\n\n    var defaults = this.defaults = {\n      animation: 'am-fade',\n      template: 'tab/tab.tpl.html',\n      navClass: 'nav-tabs',\n      activeClass: 'active'\n    };\n\n    var controller = this.controller = function($scope, $element, $attrs) {\n      var self = this;\n\n      // Attributes options\n      self.$options = angular.copy(defaults);\n      angular.forEach(['animation', 'navClass', 'activeClass'], function(key) {\n        if(angular.isDefined($attrs[key])) self.$options[key] = $attrs[key];\n      });\n\n      // Publish options on scope\n      $scope.$navClass = self.$options.navClass;\n      $scope.$activeClass = self.$options.activeClass;\n\n      self.$panes = $scope.$panes = [];\n\n      // DEPRECATED: $viewChangeListeners, please use $activePaneChangeListeners\n      // Because we deprecated ngModel usage, we rename viewChangeListeners to \n      // activePaneChangeListeners to make more sense.\n      self.$activePaneChangeListeners = self.$viewChangeListeners = [];\n\n      self.$push = function(pane) {\n        self.$panes.push(pane);\n      };\n\n      self.$remove = function(pane) {\n        var index = self.$panes.indexOf(pane);\n        var activeIndex = self.$panes.$active;\n\n        // remove pane from $panes array\n        self.$panes.splice(index, 1);\n\n        if (index < activeIndex) {\n          // we removed a pane before the active pane, so we need to \n          // decrement the active pane index\n          activeIndex--;\n        }\n        else if (index === activeIndex && activeIndex === self.$panes.length) {\n          // we remove the active pane and it was the one at the end,\n          // so select the previous one\n          activeIndex--;\n        }\n        self.$setActive(activeIndex);\n      };\n\n      self.$panes.$active = 0;\n      self.$setActive = $scope.$setActive = function(value) {\n        self.$panes.$active = value;\n        self.$activePaneChangeListeners.forEach(function(fn) {\n          fn();\n        });\n      };\n\n    };\n\n    this.$get = function() {\n      var $tab = {};\n      $tab.defaults = defaults;\n      $tab.controller = controller;\n      return $tab;\n    };\n\n  })\n\n  .directive('bsTabs', function($window, $animate, $tab, $parse) {\n\n    var defaults = $tab.defaults;\n\n    return {\n      require: ['?ngModel', 'bsTabs'],\n      transclude: true,\n      scope: true,\n      controller: ['$scope', '$element', '$attrs', $tab.controller],\n      templateUrl: function(element, attr) {\n        return attr.template || defaults.template;\n      },\n      link: function postLink(scope, element, attrs, controllers) {\n\n        var ngModelCtrl = controllers[0];\n        var bsTabsCtrl = controllers[1];\n\n        // DEPRECATED: ngModel, please use bsActivePane\n        // 'ngModel' is deprecated bacause if interferes with form validation\n        // and status, so avoid using it here.\n        if(ngModelCtrl) {\n          console.warn('Usage of ngModel is deprecated, please use bsActivePane instead!');\n\n          // Update the modelValue following\n          bsTabsCtrl.$activePaneChangeListeners.push(function() {\n            ngModelCtrl.$setViewValue(bsTabsCtrl.$panes.$active);\n          });\n\n          // modelValue -> $formatters -> viewValue\n          ngModelCtrl.$formatters.push(function(modelValue) {\n            // console.warn('$formatter(\"%s\"): modelValue=%o (%o)', element.attr('ng-model'), modelValue, typeof modelValue);\n            bsTabsCtrl.$setActive(modelValue * 1);\n            return modelValue;\n          });\n\n        }\n\n        if (attrs.bsActivePane) {\n          // adapted from angularjs ngModelController bindings\n          // https://github.com/angular/angular.js/blob/v1.3.1/src%2Fng%2Fdirective%2Finput.js#L1730\n          var parsedBsActivePane = $parse(attrs.bsActivePane);\n\n          // Update bsActivePane value with change\n          bsTabsCtrl.$activePaneChangeListeners.push(function() {\n            parsedBsActivePane.assign(scope, bsTabsCtrl.$panes.$active);\n          });\n\n          // watch bsActivePane for value changes\n          scope.$watch(attrs.bsActivePane, function(newValue, oldValue) {\n            bsTabsCtrl.$setActive(newValue * 1);\n          }, true);\n        }\n      }\n    };\n\n  })\n\n  .directive('bsPane', function($window, $animate, $sce) {\n\n    return {\n      require: ['^?ngModel', '^bsTabs'],\n      scope: true,\n      link: function postLink(scope, element, attrs, controllers) {\n\n        var ngModelCtrl = controllers[0];\n        var bsTabsCtrl = controllers[1];\n\n        // Add base class\n        element.addClass('tab-pane');\n\n        // Observe title attribute for change\n        attrs.$observe('title', function(newValue, oldValue) {\n          scope.title = $sce.trustAsHtml(newValue);\n        });\n\n        // Add animation class\n        if(bsTabsCtrl.$options.animation) {\n          element.addClass(bsTabsCtrl.$options.animation);\n        }\n\n        // Push pane to parent bsTabs controller\n        bsTabsCtrl.$push(scope);\n\n        // remove pane from tab controller when pane is destroyed\n        scope.$on('$destroy', function() {\n          bsTabsCtrl.$remove(scope);\n        });\n\n        function render() {\n          var index = bsTabsCtrl.$panes.indexOf(scope);\n          var active = bsTabsCtrl.$panes.$active;\n          $animate[index === active ? 'addClass' : 'removeClass'](element, bsTabsCtrl.$options.activeClass);\n        }\n\n        bsTabsCtrl.$activePaneChangeListeners.push(function() {\n          render();\n        });\n        render();\n\n      }\n    };\n\n  });\n"], "sourceRoot": "/source/"}