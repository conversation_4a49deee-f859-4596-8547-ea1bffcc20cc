{"version": 3, "sources": ["collapse/collapse.js"], "names": [], "mappings": "qBASM,OAAA,uCAEA,YAAe,gCAGjB,UAAI,cACF,gBAAW,qCAGX,eAAK,uDA2ED,yBAAI,EAAA,EAAA,EAAA,EAA0B,OAAS,IACrC,EAAA,EAAwB,kBAMxB,EAAc,KAAK,EAAS,SAAA,SAChC,EAAO,GAAY,EAAQ,SAAA,OAAgB,WAKvC,GAAW,MACb,GAAc,EAAA,SAAe,sCAIjC,QAAS,GAAa,GACpB,GAAI,GAAM,EAAA,SAAS,QAAe,QAAA,WAEhC,EAAK,SAAS,QAAQ,OAAO,EAAG,WAI3B,GAAS,4DAMG,KAAlB,EAAO,SAAA,QAAW,QAAA,IACjB,EAAA,SAAY,QAAA,KAAA,yCAnGhB,QAAK,SAAA,YAAuB,iBAAA,cAAA,iBAAA,iBAAA,SAAA,iEAK5B,EAAK,sCAIL,EAAK,gBAAA,SAAoB,GACvB,EAAI,SAAQ,KAAK,MAEjB,gBAAqB,SAAO,8IAU1B,GAAA,EAAe,SAAA,QAAA,oDAOf,EAAA,QAMJ,EAAK,qBAAoB,QAAA,SAAa,GACpC,SAKE,SAAS,QAAS,EAAA,SAAe,mBAAS,gBACrC,EAAA,WAAA,SAAA,GACL,QAAA,QAAa,sCAGV,EAAA,SAAA,qCAOH,qBAAsB,QAAA,SAAe,sCAMvC,MAAI,GAAA,SAAgB,cAAc,EAAA,SAAA,QACD,IAAjC,EAAI,SAAW,QAAI,OAAc,EAAQ,SAAK,QAAA,GAAA,kCAiDlD,OAFD,GAAU,SAAA,iBAEL,eAKI,cAAS,UAAgB,WAAS,YAAO,SAAa,EAAA,EAAA,GAEtD,EAAc,yBAGf,WAAa,+FAIZ,GAAY,EAAc,gBAMd,qBAAqB,KAAA,mDAK5B,YAAA,KAAA,SAAA,2BAKG,WAAc,yCAKX,QAAA,wBAIX,EAAO,WAAA,EAAA,yDAiBP,mBAAiB,2BAGb,YAAK,sCAGb,OAAe,EAAA,WAIb,KAAA,cAAe,YAGjB,EAAW,gBAAoB,KAG7B,IAAM,WAAA,yJAeJ,oBAAc,WAAY,SAAA,gHA4BnB,EAAkB,iBACzB,EAAS,qDAGX,EAAS,YAGX,IAAe,IACb,EAAA,2CA7BF,GACE,IADgB,EAAS,GACR,EAAA,iDAOnB,EAAU,SAAA,EAAuB,SAAA,WAIjC,EAAS,gBAAS,KAGZ,IAAA,WAAS,WACb,EAAY,kBAAiB,4CAoBlC", "file": "collapse.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.collapse', [])\n\n  .provider('$collapse', function() {\n\n    var defaults = this.defaults = {\n      animation: 'am-collapse',\n      disallowToggle: false,\n      activeClass: 'in',\n      startCollapsed: false,\n      allowMultiple: false\n    };\n\n    var controller = this.controller = function($scope, $element, $attrs) {\n      var self = this;\n\n      // Attributes options\n      self.$options = angular.copy(defaults);\n      angular.forEach(['animation', 'disallowToggle', 'activeClass', 'startCollapsed', 'allowMultiple'], function (key) {\n        if(angular.isDefined($attrs[key])) self.$options[key] = $attrs[key];\n      });\n\n      self.$toggles = [];\n      self.$targets = [];\n\n      self.$viewChangeListeners = [];\n\n      self.$registerToggle = function(element) {\n        self.$toggles.push(element);\n      };\n      self.$registerTarget = function(element) {\n        self.$targets.push(element);\n      };\n\n      self.$unregisterToggle = function(element) {\n        var index = self.$toggles.indexOf(element);\n        // remove toggle from $toggles array\n        self.$toggles.splice(index, 1);\n      };\n      self.$unregisterTarget = function(element) {\n        var index = self.$targets.indexOf(element);\n\n        // remove element from $targets array\n        self.$targets.splice(index, 1);\n\n        if (self.$options.allowMultiple) {\n          // remove target index from $active array values\n          deactivateItem(element);\n        }\n\n        // fix active item indexes\n        fixActiveItemIndexes(index);\n\n        self.$viewChangeListeners.forEach(function(fn) {\n          fn();\n        });\n      };\n\n      // use array to store all the currently open panels\n      self.$targets.$active = !self.$options.startCollapsed ? [0] : [];\n      self.$setActive = $scope.$setActive = function(value) {\n        if(angular.isArray(value)) {\n          self.$targets.$active = angular.copy(value);\n        }\n        else if(!self.$options.disallowToggle) {\n          // toogle element active status\n          isActive(value) ? deactivateItem(value) : activateItem(value);\n        } else {\n          activateItem(value);\n        }\n\n        self.$viewChangeListeners.forEach(function(fn) {\n          fn();\n        });\n      };\n\n      self.$activeIndexes = function() {\n        return self.$options.allowMultiple ? self.$targets.$active :\n          self.$targets.$active.length === 1 ? self.$targets.$active[0] : -1;\n      };\n\n      function fixActiveItemIndexes(index) {\n        // item with index was removed, so we\n        // need to adjust other items index values\n        var activeIndexes = self.$targets.$active;\n        for(var i = 0; i < activeIndexes.length; i++) {\n          if (index < activeIndexes[i]) {\n            activeIndexes[i] = activeIndexes[i] - 1;\n          }\n\n          // the last item is active, so we need to\n          // adjust its index\n          if (activeIndexes[i] === self.$targets.length) {\n            activeIndexes[i] = self.$targets.length - 1;\n          }\n        }\n      }\n\n      function isActive(value) {\n        var activeItems = self.$targets.$active;\n        return activeItems.indexOf(value) === -1 ? false : true;\n      }\n\n      function deactivateItem(value) {\n        var index = self.$targets.$active.indexOf(value);\n        if (index !== -1) {\n          self.$targets.$active.splice(index, 1);\n        }\n      }\n\n      function activateItem(value) {\n        if (!self.$options.allowMultiple) {\n          // remove current selected item\n          self.$targets.$active.splice(0, 1);\n        }\n\n        if (self.$targets.$active.indexOf(value) === -1) {\n          self.$targets.$active.push(value);\n        }\n      }\n\n    };\n\n    this.$get = function() {\n      var $collapse = {};\n      $collapse.defaults = defaults;\n      $collapse.controller = controller;\n      return $collapse;\n    };\n\n  })\n\n  .directive('bsCollapse', function($window, $animate, $collapse) {\n\n    var defaults = $collapse.defaults;\n\n    return {\n      require: ['?ngModel', 'bsCollapse'],\n      controller: ['$scope', '$element', '$attrs', $collapse.controller],\n      link: function postLink(scope, element, attrs, controllers) {\n\n        var ngModelCtrl = controllers[0];\n        var bsCollapseCtrl = controllers[1];\n\n        if(ngModelCtrl) {\n\n          // Update the modelValue following\n          bsCollapseCtrl.$viewChangeListeners.push(function() {\n            ngModelCtrl.$setViewValue(bsCollapseCtrl.$activeIndexes());\n          });\n\n          // modelValue -> $formatters -> viewValue\n          ngModelCtrl.$formatters.push(function(modelValue) {\n            // console.warn('$formatter(\"%s\"): modelValue=%o (%o)', element.attr('ng-model'), modelValue, typeof modelValue);\n            if (angular.isArray(modelValue)) {\n              // model value is an array, so just replace\n              // the active items directly\n              bsCollapseCtrl.$setActive(modelValue);\n            }\n            else {\n              var activeIndexes = bsCollapseCtrl.$activeIndexes();\n\n              if (angular.isArray(activeIndexes)) {\n                // we have an array of selected indexes\n                if (activeIndexes.indexOf(modelValue * 1) === -1) {\n                  // item with modelValue index is not active\n                  bsCollapseCtrl.$setActive(modelValue * 1);\n                }\n              }\n              else if (activeIndexes !== modelValue * 1) {\n                bsCollapseCtrl.$setActive(modelValue * 1);\n              }\n            }\n            return modelValue;\n          });\n\n        }\n\n      }\n    };\n\n  })\n\n  .directive('bsCollapseToggle', function() {\n\n    return {\n      require: ['^?ngModel', '^bsCollapse'],\n      link: function postLink(scope, element, attrs, controllers) {\n\n        var ngModelCtrl = controllers[0];\n        var bsCollapseCtrl = controllers[1];\n\n        // Add base attr\n        element.attr('data-toggle', 'collapse');\n\n        // Push pane to parent bsCollapse controller\n        bsCollapseCtrl.$registerToggle(element);\n\n        // remove toggle from collapse controller when toggle is destroyed\n        scope.$on('$destroy', function() {\n          bsCollapseCtrl.$unregisterToggle(element);\n        });\n\n        element.on('click', function() {\n          var index = attrs.bsCollapseToggle || bsCollapseCtrl.$toggles.indexOf(element);\n          bsCollapseCtrl.$setActive(index * 1);\n          scope.$apply();\n        });\n\n      }\n    };\n\n  })\n\n  .directive('bsCollapseTarget', function($animate) {\n\n    return {\n      require: ['^?ngModel', '^bsCollapse'],\n      // scope: true,\n      link: function postLink(scope, element, attrs, controllers) {\n\n        var ngModelCtrl = controllers[0];\n        var bsCollapseCtrl = controllers[1];\n\n        // Add base class\n        element.addClass('collapse');\n\n        // Add animation class\n        if(bsCollapseCtrl.$options.animation) {\n          element.addClass(bsCollapseCtrl.$options.animation);\n        }\n\n        // Push pane to parent bsCollapse controller\n        bsCollapseCtrl.$registerTarget(element);\n\n        // remove pane target from collapse controller when target is destroyed\n        scope.$on('$destroy', function() {\n          bsCollapseCtrl.$unregisterTarget(element);\n        });\n\n        function render() {\n          var index = bsCollapseCtrl.$targets.indexOf(element);\n          var active = bsCollapseCtrl.$activeIndexes();\n          var action = 'removeClass';\n          if (angular.isArray(active)) {\n            if (active.indexOf(index) !== -1) {\n              action = 'addClass';\n            }\n          }\n          else if (index === active) {\n            action = 'addClass';\n          }\n\n          $animate[action](element, bsCollapseCtrl.$options.activeClass);\n        }\n\n        bsCollapseCtrl.$viewChangeListeners.push(function() {\n          render();\n        });\n        render();\n\n      }\n    };\n\n  });\n"], "sourceRoot": "/source/"}