{"name": "flot-spline", "homepage": "https://github.com/JohnPozy/flot-spline", "authors": ["<PERSON> < <EMAIL> >"], "description": "Flot plugin that provides spline interpolation for line graphs", "keywords": ["j<PERSON>y", "flot", "spline"], "license": "MIT", "main": "js/jquery.flot.spline.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"], "_release": "56363f8802", "_resolution": {"type": "branch", "branch": "master", "commit": "56363f8802b73dc2972a77d083ed36233f7869d7"}, "_source": "git://github.com/JohnPozy/flot-spline.git", "_target": "*", "_originalSource": "flot-spline", "_direct": true}