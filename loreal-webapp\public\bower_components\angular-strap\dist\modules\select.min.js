/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.select",["mgcrea.ngStrap.tooltip","mgcrea.ngStrap.helpers.parseOptions"]).provider("$select",function(){var e=this.defaults={animation:"am-fade",prefixClass:"select",prefixEvent:"$select",placement:"bottom-left",template:"select/select.tpl.html",trigger:"focus",container:!1,keyboard:!0,html:!1,delay:0,multiple:!1,allNoneButtons:!1,sort:!0,caretHtml:'&nbsp;<span class="caret"></span>',placeholder:"Choose among the following...",allText:"All",noneText:"None",maxLength:3,maxLengthHtml:"selected",iconCheckmark:"glyphicon glyphicon-ok"};this.$get=["$window","$document","$rootScope","$tooltip","$timeout",function(t,n,a,l,i){function o(t,n,a){var o={},c=angular.extend({},e,a);o=l(t,c);var u=o.$scope;u.$matches=[],u.$activeIndex=0,u.$isMultiple=c.multiple,u.$showAllNoneButtons=c.allNoneButtons&&c.multiple,u.$iconCheckmark=c.iconCheckmark,u.$allText=c.allText,u.$noneText=c.noneText,u.$activate=function(e){u.$$postDigest(function(){o.activate(e)})},u.$select=function(e){u.$$postDigest(function(){o.select(e)})},u.$isVisible=function(){return o.$isVisible()},u.$isActive=function(e){return o.$isActive(e)},u.$selectAll=function(){for(var e=0;e<u.$matches.length;e++)u.$isActive(e)||u.$select(e)},u.$selectNone=function(){for(var e=0;e<u.$matches.length;e++)u.$isActive(e)&&u.$select(e)},o.update=function(e){u.$matches=e,o.$updateActiveIndex()},o.activate=function(e){return c.multiple?(u.$activeIndex.sort(),o.$isActive(e)?u.$activeIndex.splice(u.$activeIndex.indexOf(e),1):u.$activeIndex.push(e),c.sort&&u.$activeIndex.sort()):u.$activeIndex=e,u.$activeIndex},o.select=function(e){var t=u.$matches[e].value;u.$apply(function(){o.activate(e),c.multiple?n.$setViewValue(u.$activeIndex.map(function(e){return u.$matches[e].value})):(n.$setViewValue(t),o.hide())}),u.$emit(c.prefixEvent+".select",t,e,o)},o.$updateActiveIndex=function(){n.$modelValue&&u.$matches.length?u.$activeIndex=c.multiple&&angular.isArray(n.$modelValue)?n.$modelValue.map(function(e){return o.$getIndex(e)}):o.$getIndex(n.$modelValue):u.$activeIndex>=u.$matches.length&&(u.$activeIndex=c.multiple?[]:0)},o.$isVisible=function(){return c.minLength&&n?u.$matches.length&&n.$viewValue.length>=c.minLength:u.$matches.length},o.$isActive=function(e){return c.multiple?-1!==u.$activeIndex.indexOf(e):u.$activeIndex===e},o.$getIndex=function(e){var t=u.$matches.length,n=t;if(t){for(n=t;n--&&u.$matches[n].value!==e;);if(!(0>n))return n}},o.$onMouseDown=function(e){if(e.preventDefault(),e.stopPropagation(),r){var t=angular.element(e.target);t.triggerHandler("click")}},o.$onKeyDown=function(e){if(/(9|13|38|40)/.test(e.keyCode)){if(e.preventDefault(),e.stopPropagation(),!c.multiple&&(13===e.keyCode||9===e.keyCode))return o.select(u.$activeIndex);38===e.keyCode&&u.$activeIndex>0?u.$activeIndex--:40===e.keyCode&&u.$activeIndex<u.$matches.length-1?u.$activeIndex++:angular.isUndefined(u.$activeIndex)&&(u.$activeIndex=0),u.$digest()}};var s=o.show;o.show=function(){s(),c.multiple&&o.$element.addClass("select-multiple"),i(function(){o.$element.on(r?"touchstart":"mousedown",o.$onMouseDown),c.keyboard&&t.on("keydown",o.$onKeyDown)},0,!1)};var $=o.hide;return o.hide=function(){o.$element.off(r?"touchstart":"mousedown",o.$onMouseDown),c.keyboard&&t.off("keydown",o.$onKeyDown),$(!0)},o}var c=(angular.element(t.document.body),/(ip(a|o)d|iphone|android)/gi.test(t.navigator.userAgent)),r="createTouch"in t.document&&c;return o.defaults=e,o}]}).directive("bsSelect",["$window","$parse","$q","$select","$parseOptions",function(e,t,n,a,l){var i=a.defaults;return{restrict:"EAC",require:"ngModel",link:function(e,t,n,o){var c={scope:e,placeholder:i.placeholder};if(angular.forEach(["placement","container","delay","trigger","keyboard","html","animation","template","placeholder","multiple","allNoneButtons","maxLength","maxLengthHtml","allText","noneText","iconCheckmark","autoClose","id"],function(e){angular.isDefined(n[e])&&(c[e]=n[e])}),"select"===t[0].nodeName.toLowerCase()){var r=t;r.css("display","none"),t=angular.element('<button type="button" class="btn btn-default"></button>'),r.after(t)}var u=l(n.ngOptions),s=a(t,o,c),$=u.$match[7].replace(/\|.+/,"").trim();e.$watch($,function(){u.valuesFn(e,o).then(function(e){s.update(e),o.$render()})},!0),e.$watch(n.ngModel,function(){s.$updateActiveIndex(),o.$render()},!0),o.$render=function(){var e,n;c.multiple&&angular.isArray(o.$modelValue)?(e=o.$modelValue.map(function(e){return n=s.$getIndex(e),angular.isDefined(n)?s.$scope.$matches[n].label:!1}).filter(angular.isDefined),e=e.length>(c.maxLength||i.maxLength)?e.length+" "+(c.maxLengthHtml||i.maxLengthHtml):e.join(", ")):(n=s.$getIndex(o.$modelValue),e=angular.isDefined(n)?s.$scope.$matches[n].label:!1),t.html((e?e:c.placeholder)+i.caretHtml)},c.multiple&&(o.$isEmpty=function(e){return!e||0===e.length}),e.$on("$destroy",function(){s&&s.destroy(),c=null,s=null})}}}]);
//# sourceMappingURL=select.min.js.map