/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.modal",["mgcrea.ngStrap.helpers.dimensions"]).provider("$modal",function(){var e=this.defaults={animation:"am-fade",backdropAnimation:"am-fade",prefixClass:"modal",prefixEvent:"modal",placement:"top",template:"modal/modal.tpl.html",contentTemplate:!1,container:!1,element:null,backdrop:!0,keyboard:!0,html:!1,show:!0};this.$get=["$window","$rootScope","$compile","$q","$templateCache","$http","$animate","$timeout","$sce","dimensions",function(n,t,o,a,i,r,l,c,s){function u(n){function a(){b.$emit(w.prefixEvent+".show",u)}function i(){b.$emit(w.prefixEvent+".hide",u),g.removeClass(w.prefixClass+"-open"),w.animation&&g.removeClass(w.prefixClass+"-with-"+w.animation)}function r(e){e.target===e.currentTarget&&("static"===w.backdrop?u.focus():u.hide())}function c(e){e.preventDefault()}var u={},w=u.$options=angular.extend({},e,n);u.$promise=p(w.template);var b=u.$scope=w.scope&&w.scope.$new()||t.$new();w.element||w.container||(w.container="body"),u.$id=w.id||w.element&&w.element.attr("id")||"",f(["title","content"],function(e){w[e]&&(b[e]=s.trustAsHtml(w[e]))}),b.$hide=function(){b.$$postDigest(function(){u.hide()})},b.$show=function(){b.$$postDigest(function(){u.show()})},b.$toggle=function(){b.$$postDigest(function(){u.toggle()})},u.$isShown=b.$isShown=!1,w.contentTemplate&&(u.$promise=u.$promise.then(function(e){var t=angular.element(e);return p(w.contentTemplate).then(function(e){var o=m('[ng-bind="content"]',t[0]).removeAttr("ng-bind").html(e);return n.template||o.next().remove(),t[0].outerHTML})}));var k,y,C=angular.element('<div class="'+w.prefixClass+'-backdrop"/>');return u.$promise.then(function(e){angular.isObject(e)&&(e=e.data),w.html&&(e=e.replace(v,'ng-bind-html="')),e=h.apply(e),k=o(e),u.init()}),u.init=function(){w.show&&b.$$postDigest(function(){u.show()})},u.destroy=function(){y&&(y.remove(),y=null),C&&(C.remove(),C=null),b.$destroy()},u.show=function(){if(!u.$isShown&&!b.$emit(w.prefixEvent+".show.before",u).defaultPrevented){var e,n;angular.isElement(w.container)?(e=w.container,n=w.container[0].lastChild?angular.element(w.container[0].lastChild):null):w.container?(e=m(w.container),n=e[0].lastChild?angular.element(e[0].lastChild):null):(e=null,n=w.element),y=u.$element=k(b,function(){}),y.css({display:"block"}).addClass(w.placement),w.animation&&(w.backdrop&&C.addClass(w.backdropAnimation),y.addClass(w.animation)),w.backdrop&&l.enter(C,g,null);var t=l.enter(y,e,n,a);t&&t.then&&t.then(a),u.$isShown=b.$isShown=!0,d(b);var o=y[0];$(function(){o.focus()}),g.addClass(w.prefixClass+"-open"),w.animation&&g.addClass(w.prefixClass+"-with-"+w.animation),w.backdrop&&(y.on("click",r),C.on("click",r),C.on("wheel",c)),w.keyboard&&y.on("keyup",u.$onKeyUp)}},u.hide=function(){if(u.$isShown&&!b.$emit(w.prefixEvent+".hide.before",u).defaultPrevented){var e=l.leave(y,i);e&&e.then&&e.then(i),w.backdrop&&l.leave(C),u.$isShown=b.$isShown=!1,d(b),w.backdrop&&(y.off("click",r),C.off("click",r),C.off("wheel",c)),w.keyboard&&y.off("keyup",u.$onKeyUp)}},u.toggle=function(){u.$isShown?u.hide():u.show()},u.focus=function(){y[0].focus()},u.$onKeyUp=function(e){27===e.which&&u.$isShown&&(u.hide(),e.stopPropagation())},u}function d(e){e.$$phase||e.$root&&e.$root.$$phase||e.$digest()}function m(e,n){return angular.element((n||document).querySelectorAll(e))}function p(e){return w[e]?w[e]:w[e]=a.when(i.get(e)||r.get(e)).then(function(n){return angular.isObject(n)?(i.put(e,n.data),n.data):n})}var f=angular.forEach,h=String.prototype.trim,$=n.requestAnimationFrame||n.setTimeout,g=angular.element(n.document.body),v=/ng-bind="/gi,w={};return u}]}).directive("bsModal",["$window","$sce","$modal",function(e,n,t){return{restrict:"EAC",scope:!0,link:function(e,o,a){var i={scope:e,element:o,show:!1};angular.forEach(["template","contentTemplate","placement","backdrop","keyboard","html","container","animation","id"],function(e){angular.isDefined(a[e])&&(i[e]=a[e])}),angular.forEach(["title","content"],function(t){a[t]&&a.$observe(t,function(o){e[t]=n.trustAsHtml(o)})}),a.bsModal&&e.$watch(a.bsModal,function(n){angular.isObject(n)?angular.extend(e,n):e.content=n},!0);var r=t(i);o.on(a.trigger||"click",r.toggle),e.$on("$destroy",function(){r&&r.destroy(),i=null,r=null})}}}]);
//# sourceMappingURL=modal.min.js.map