{"version": 3, "sources": ["helpers/parse-options.js"], "names": [], "mappings": "sIAcY,qLAGJ,MAAI,SAAU,KAAQ,SAAW,EAAA,iFA0CjC,GAAA,GAAc,EACd,EAAO,EAAA,EAAA,wCAvCP,GAAI,MAGF,EAAA,QAAc,UAAiB,EAAK,KACxB,cAGZ,GAAA,EAAmB,EAAY,EAAA,EAAA,EAAA,WAEpB,KAAO,wDAGpB,EAAc,EAAA,IAAW,EAAA,GACvB,EAAU,EAAK,KACT,EAAS,EAAQ,IAAA,MACrB,EAAc,EAAA,GAAU,EAAA,GAAS,KAC1B,EAAA,EAAc,8BAIzB,MAAA,GAAA,KAAc,EAAA,EAAe,IAC3B,KAAI,SAAQ,GAEZ,MADA,GAAM,QAAa,EAAA,EAAA,EAAA,MACZ,EAAU,sCAKnB,GAAA,YACE,GAAA,GAAkB,EACZ,EAAS", "file": "parse-options.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.helpers.parseOptions', [])\n\n  .provider('$parseOptions', function() {\n\n    var defaults = this.defaults = {\n      regexp: /^\\s*(.*?)(?:\\s+as\\s+(.*?))?(?:\\s+group\\s+by\\s+(.*))?\\s+for\\s+(?:([\\$\\w][\\$\\w]*)|(?:\\(\\s*([\\$\\w][\\$\\w]*)\\s*,\\s*([\\$\\w][\\$\\w]*)\\s*\\)))\\s+in\\s+(.*?)(?:\\s+track\\s+by\\s+(.*?))?$/\n    };\n\n    this.$get = function($parse, $q) {\n\n      function ParseOptionsFactory(attr, config) {\n\n        var $parseOptions = {};\n\n        // Common vars\n        var options = angular.extend({}, defaults, config);\n        $parseOptions.$values = [];\n\n        // Private vars\n        var match, displayFn, valueName, keyName, groupByFn, valueFn, valuesFn;\n\n        $parseOptions.init = function() {\n          $parseOptions.$match = match = attr.match(options.regexp);\n          displayFn = $parse(match[2] || match[1]),\n          valueName = match[4] || match[6],\n          keyName = match[5],\n          groupByFn = $parse(match[3] || ''),\n          valueFn = $parse(match[2] ? match[1] : valueName),\n          valuesFn = $parse(match[7]);\n        };\n\n        $parseOptions.valuesFn = function(scope, controller) {\n          return $q.when(valuesFn(scope, controller))\n          .then(function(values) {\n            $parseOptions.$values = values ? parseValues(values, scope) : {};\n            return $parseOptions.$values;\n          });\n        };\n\n        $parseOptions.displayValue = function(modelValue) {\n          var scope = {};\n          scope[valueName] = modelValue;\n          return displayFn(scope);\n        };\n\n        // Private functions\n\n        function parseValues(values, scope) {\n          return values.map(function(match, index) {\n            var locals = {}, label, value;\n            locals[valueName] = match;\n            label = displayFn(scope, locals);\n            value = valueFn(scope, locals);\n            return {label: label, value: value, index: index};\n          });\n        }\n\n        $parseOptions.init();\n        return $parseOptions;\n\n      }\n\n      return ParseOptionsFactory;\n\n    };\n\n  });\n"], "sourceRoot": "/source/"}