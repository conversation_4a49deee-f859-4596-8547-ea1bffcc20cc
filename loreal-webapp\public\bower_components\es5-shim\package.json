{"name": "es5-shim", "version": "4.0.5", "description": "ECMAScript 5 compatibility shims for legacy JavaScript engines", "homepage": "http://github.com/es-shims/es5-shim/", "contributors": ["<PERSON> <<EMAIL>> (http://github.com/kriskowal/)", "<PERSON> <<EMAIL>> (http://samhuri.net/)", "<PERSON><PERSON><PERSON> <<EMAIL>> (http://github.com/fschaefer)", "<PERSON><PERSON><PERSON> <<EMAIL>> (http://jeditoolkit.com)", "<PERSON> <<EMAIL>> (http://kitcambridge.github.com)", "<PERSON> <<EMAIL>> (https://github.com/ljharb/)"], "bugs": {"mail": "<EMAIL>", "url": "http://github.com/es-shims/es5-shim/issues"}, "licenses": [{"type": "MIT", "url": "http://github.com/es-shims/es5-shim/raw/master/LICENSE"}], "main": "es5-shim.js", "repository": {"type": "git", "url": "http://github.com/es-shims/es5-shim.git"}, "scripts": {"minify": "npm run minify-shim && npm run minify-sham", "minify-shim": "uglifyjs es5-shim.js --comments --source-map=es5-shim.map -m -b ascii_only=true,beautify=false > es5-shim.min.js", "minify-sham": "uglifyjs es5-sham.js --comments --source-map=es5-sham.map -m -b ascii_only=true,beautify=false > es5-sham.min.js", "test": "npm run lint && jasmine-node --matchall ./ tests/spec/", "test-native": "jasmine-node --matchall tests/spec/", "lint": "jscs tests/helpers/*.js tests/spec/*.js es5-shim.js es5-sham.js"}, "devDependencies": {"jasmine-node": "~1.14.5", "jscs": "~1.8.0", "uglify-js": "~2.4.15"}, "engines": {"node": ">=0.4.0"}, "testling": {"browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/18.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/25.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "keywords": ["shim", "es5", "es5 shim", "javascript", "ecmascript", "polyfill"]}