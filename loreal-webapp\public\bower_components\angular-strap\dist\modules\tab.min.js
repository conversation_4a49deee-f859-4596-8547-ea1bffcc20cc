/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.tab",[]).provider("$tab",function(){var e=this.defaults={animation:"am-fade",template:"tab/tab.tpl.html",navClass:"nav-tabs",activeClass:"active"},a=this.controller=function(a,n,t){var s=this;s.$options=angular.copy(e),angular.forEach(["animation","navClass","activeClass"],function(e){angular.isDefined(t[e])&&(s.$options[e]=t[e])}),a.$navClass=s.$options.navClass,a.$activeClass=s.$options.activeClass,s.$panes=a.$panes=[],s.$activePaneChangeListeners=s.$viewChangeListeners=[],s.$push=function(e){s.$panes.push(e)},s.$remove=function(e){var a=s.$panes.indexOf(e),n=s.$panes.$active;s.$panes.splice(a,1),n>a?n--:a===n&&n===s.$panes.length&&n--,s.$setActive(n)},s.$panes.$active=0,s.$setActive=a.$setActive=function(e){s.$panes.$active=e,s.$activePaneChangeListeners.forEach(function(e){e()})}};this.$get=function(){var n={};return n.defaults=e,n.controller=a,n}}).directive("bsTabs",["$window","$animate","$tab","$parse",function(e,a,n,t){var s=n.defaults;return{require:["?ngModel","bsTabs"],transclude:!0,scope:!0,controller:["$scope","$element","$attrs",n.controller],templateUrl:function(e,a){return a.template||s.template},link:function(e,a,n,s){var i=s[0],o=s[1];if(i&&(console.warn("Usage of ngModel is deprecated, please use bsActivePane instead!"),o.$activePaneChangeListeners.push(function(){i.$setViewValue(o.$panes.$active)}),i.$formatters.push(function(e){return o.$setActive(1*e),e})),n.bsActivePane){var c=t(n.bsActivePane);o.$activePaneChangeListeners.push(function(){c.assign(e,o.$panes.$active)}),e.$watch(n.bsActivePane,function(e){o.$setActive(1*e)},!0)}}}}]).directive("bsPane",["$window","$animate","$sce",function(e,a,n){return{require:["^?ngModel","^bsTabs"],scope:!0,link:function(e,t,s,i){function o(){var n=c.$panes.indexOf(e),s=c.$panes.$active;a[n===s?"addClass":"removeClass"](t,c.$options.activeClass)}var c=(i[0],i[1]);t.addClass("tab-pane"),s.$observe("title",function(a){e.title=n.trustAsHtml(a)}),c.$options.animation&&t.addClass(c.$options.animation),c.$push(e),e.$on("$destroy",function(){c.$remove(e)}),c.$activePaneChangeListeners.push(function(){o()}),o()}}}]);
//# sourceMappingURL=tab.min.js.map