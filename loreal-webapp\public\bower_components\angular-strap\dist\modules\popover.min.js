/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.popover",["mgcrea.ngStrap.tooltip"]).provider("$popover",function(){var t=this.defaults={animation:"am-fade",customClass:"",container:!1,target:!1,placement:"right",template:"popover/popover.tpl.html",contentTemplate:!1,trigger:"click",keyboard:!0,html:!1,title:"",content:"",delay:0,autoClose:!1};this.$get=["$tooltip",function(e){function n(n,o){var a=angular.extend({},t,o),r=e(n,a);return a.content&&(r.$scope.content=a.content),r}return n}]}).directive("bsPopover",["$window","$sce","$popover",function(t,e,n){var o=t.requestAnimationFrame||t.setTimeout;return{restrict:"EAC",scope:!0,link:function(t,a,r){var i={scope:t};angular.forEach(["template","contentTemplate","placement","container","target","delay","trigger","keyboard","html","animation","customClass","autoClose","id"],function(t){angular.isDefined(r[t])&&(i[t]=r[t])}),angular.forEach(["title","content"],function(n){r[n]&&r.$observe(n,function(a,r){t[n]=e.trustAsHtml(a),angular.isDefined(r)&&o(function(){c&&c.$applyPlacement()})})}),r.bsPopover&&t.$watch(r.bsPopover,function(e,n){angular.isObject(e)?angular.extend(t,e):t.content=e,angular.isDefined(n)&&o(function(){c&&c.$applyPlacement()})},!0),r.bsShow&&t.$watch(r.bsShow,function(t){c&&angular.isDefined(t)&&(angular.isString(t)&&(t=!!t.match(/true|,?(popover),?/i)),t===!0?c.show():c.hide())});var c=n(a,i);t.$on("$destroy",function(){c&&c.destroy(),i=null,c=null})}}}]);
//# sourceMappingURL=popover.min.js.map