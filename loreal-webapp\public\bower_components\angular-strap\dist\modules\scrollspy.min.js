/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.scrollspy",["mgcrea.ngStrap.helpers.debounce","mgcrea.ngStrap.helpers.dimensions"]).provider("$scrollspy",function(){var e=this.$$spies={},t=this.defaults={debounce:150,throttle:100,offset:100};this.$get=["$window","$document","$rootScope","dimensions","debounce","throttle",function(n,o,r,c,i,s){function a(e,t){return e[0].nodeName&&e[0].nodeName.toLowerCase()===t.toLowerCase()}function l(o){var l=angular.extend({},t,o);l.element||(l.element=p);var d=a(l.element,"body"),h=d?u:l.element,m=d?"window":l.id;if(e[m])return e[m].$$count++,e[m];var g,v,$,k,E,T,y,b,C={},S=C.$trackedElements=[],L=[];return C.init=function(){this.$$count=1,k=i(this.checkPosition,l.debounce),E=s(this.checkPosition,l.throttle),h.on("click",this.checkPositionWithEventLoop),u.on("resize",k),h.on("scroll",E),T=i(this.checkOffsets,l.debounce),g=r.$on("$viewContentLoaded",T),v=r.$on("$includeContentLoaded",T),T(),m&&(e[m]=C)},C.destroy=function(){this.$$count--,this.$$count>0||(h.off("click",this.checkPositionWithEventLoop),u.off("resize",k),h.off("scroll",E),g(),v(),m&&delete e[m])},C.checkPosition=function(){if(L.length){if(b=(d?n.pageYOffset:h.prop("scrollTop"))||0,y=Math.max(n.innerHeight,f.prop("clientHeight")),b<L[0].offsetTop&&$!==L[0].target)return C.$activateElement(L[0]);for(var e=L.length;e--;)if(!angular.isUndefined(L[e].offsetTop)&&null!==L[e].offsetTop&&$!==L[e].target&&!(b<L[e].offsetTop||L[e+1]&&b>L[e+1].offsetTop))return C.$activateElement(L[e])}},C.checkPositionWithEventLoop=function(){setTimeout(C.checkPosition,1)},C.$activateElement=function(e){if($){var t=C.$getTrackedElement($);t&&(t.source.removeClass("active"),a(t.source,"li")&&a(t.source.parent().parent(),"li")&&t.source.parent().parent().removeClass("active"))}$=e.target,e.source.addClass("active"),a(e.source,"li")&&a(e.source.parent().parent(),"li")&&e.source.parent().parent().addClass("active")},C.$getTrackedElement=function(e){return S.filter(function(t){return t.target===e})[0]},C.checkOffsets=function(){angular.forEach(S,function(e){var t=document.querySelector(e.target);e.offsetTop=t?c.offset(t).top:null,l.offset&&null!==e.offsetTop&&(e.offsetTop-=1*l.offset)}),L=S.filter(function(e){return null!==e.offsetTop}).sort(function(e,t){return e.offsetTop-t.offsetTop}),k()},C.trackElement=function(e,t){S.push({target:e,source:t})},C.untrackElement=function(e,t){for(var n,o=S.length;o--;)if(S[o].target===e&&S[o].source===t){n=o;break}S=S.splice(n,1)},C.activate=function(e){S[e].addClass("active")},C.init(),C}var u=angular.element(n),f=angular.element(o.prop("documentElement")),p=angular.element(n.document.body);return l}]}).directive("bsScrollspy",["$rootScope","debounce","dimensions","$scrollspy",function(e,t,n,o){return{restrict:"EAC",link:function(e,t,n){var r={scope:e};angular.forEach(["offset","target"],function(e){angular.isDefined(n[e])&&(r[e]=n[e])});var c=o(r);c.trackElement(r.target,t),e.$on("$destroy",function(){c&&(c.untrackElement(r.target,t),c.destroy()),r=null,c=null})}}}]).directive("bsScrollspyList",["$rootScope","debounce","dimensions","$scrollspy",function(){return{restrict:"A",compile:function(e){var t=e[0].querySelectorAll("li > a[href]");angular.forEach(t,function(e){var t=angular.element(e);t.parent().attr("bs-scrollspy","").attr("data-target",t.attr("href"))})}}}]);
//# sourceMappingURL=scrollspy.min.js.map