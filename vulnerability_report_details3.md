## 5.8 No CSRF Token

### Impact

- Attackers can trick logged-in users into performing unwanted actions (like changing a password or sending emails) just by clicking a link or visiting a malicious site.
- If an admin is tricked into clicking a malicious link, the attacker could perform admin-level actions.
- CSRF attacks require no visible interaction—the victim may not even realize anything has happened.

### Description

The application does not implement Cross-Site Request Forgery (CSRF) protection. This means that sensitive actions (like login, user creation, password changes, email sending, etc.) can be triggered without verifying the origin of the request.

Typically, web applications protect users by adding a unique CSRF token to each request. This token ensures that any submitted action truly originated from the legitimate user interface, not from an external or malicious source.

In your application, we observed that:

- Critical actions (e.g., POST requests to login or create users) do not contain any CSRF tokens
- The application does not validate whether a request was submitted from an authenticated session or a third-party site

### Recommendation

- Include a unique, unpredictable CSRF token in every form or state-changing request.
- Validate this token server-side before processing any action.
- If you’re using frameworks like Laravel, Django, Spring, or ASP.NET, enable built-in CSRF protection.
- Ensure token validation is active on all sensitive endpoints.
- Validate the Origin and Referer headers to ensure requests come from your domain.
- Set cookies with SameSite=Strict or SameSite=Lax to prevent them from being sent in cross-site requests.
- Implement a Web Application Firewall.
- Harden HTTP Headers.

---

## 5.9 Host Header Injection

### Impact

- If the application uses the Host header to generate URLs in emails (e.g., password reset or account activation), an attacker can change the header to their domain and trick users into clicking malicious links.
- If a caching proxy or CDN stores responses based on the Host header, it may cache malicious content for all users.
- Some internal apps use Host-based routing. Manipulating the Host header may trick backend servers into thinking the request is meant for a different internal service.
- Attackers may craft phishing links using your domain, which undermines user trust and can damage your brand reputation.

### Description

The application does not properly validate or restrict the Host header in incoming HTTP requests. This allows an attacker to supply arbitrary or malicious values in the Host header and potentially manipulate how the server behaves.

For example, if a user sends this request:

```
GET / HTTP/1.1
Host: attacker.com
```

And the server reflects or trusts this header (e.g., in redirects, password reset links, or email verification URLs), the attacker may be able to:

- Change the base URL in system-generated links (like reset-password emails)
- Redirect users to malicious sites

### Recommendation

- Configure the web server or application to only accept requests with a valid Host header (e.g., notification-vn.loreal.wans).
- Drop or reject requests with unexpected or malformed Host values.
- Never build system-generated URLs (e.g., password reset links) based on Host header alone.
- Instead, use a fixed server name or derive it from a trusted configuration value.
- Force all requests to redirect to the expected host (e.g., always redirect to https\://notification-vn.loreal.wans) if the Host header is different.

---

## 5.10 Cross-Site Tracing (XST)

### Impact

- Attackers may be able to steal session cookies by abusing the TRACE method, especially if other vulnerabilities like XSS exist.
- If authentication headers (e.g., Basic Auth or Bearer tokens) are sent with the request, they may be exposed to the attacker.
- Even without direct exploitation, TRACE provides an attacker with insight into how the server handles headers and responses, which can help in building more complex attacks.
- Leaving TRACE enabled in production environments goes against secure configuration guidelines and may trigger audit or compliance failures.

### Description

The web server has the HTTP TRACE method enabled, which is typically used for diagnostic purposes. While not inherently dangerous by itself, if the TRACE method is left enabled in a production environment, it may introduce a security vulnerability called Cross-Site Tracing (XST).

### Recommendation

- Disable TRACE.
- Implement a Web Application Firewall.
- Harden HTTP Headers.

---

## 5.11 HTTP Verb Tampering

### Impact

- Attackers may use unsupported HTTP methods (e.g., HEAD, PUT, DELETE) to bypass security filters or perform actions that were assumed to be protected.
- For example, sending a DELETE request to an API endpoint that lacks method filtering could lead to unauthorized data deletion.
- Accepting unnecessary HTTP methods opens up more ways for attackers to probe or exploit the server.

### Description

HTTP Verb Tampering occurs when a web application fails to properly restrict or validate HTTP methods (also known as verbs) such as GET, POST, PUT, DELETE, OPTIONS, or PATCH.

During testing, it was found that the application or web server responds to unexpected or unsafe HTTP methods (like TRACE, DELETE, or OPTIONS) without proper handling or restriction.

### Recommendation

- Configure your web server or application to only allow necessary HTTP methods (usually GET and POST).
- Block or return a 405 Method Not Allowed response for all others.
- Ensure that authentication and authorization checks apply regardless of HTTP method used.
- Implement logic in your application to reject or handle unexpected HTTP methods explicitly.

---

