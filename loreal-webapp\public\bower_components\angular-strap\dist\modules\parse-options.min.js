/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.helpers.parseOptions",[]).provider("$parseOptions",function(){var n=this.defaults={regexp:/^\s*(.*?)(?:\s+as\s+(.*?))?(?:\s+group\s+by\s+(.*))?\s+for\s+(?:([\$\w][\$\w]*)|(?:\(\s*([\$\w][\$\w]*)\s*,\s*([\$\w][\$\w]*)\s*\)))\s+in\s+(.*?)(?:\s+track\s+by\s+(.*?))?$/};this.$get=["$parse","$q",function(r,e){function s(s,t){function a(n,r){return n.map(function(n,e){var s,t,a={};return a[c]=n,s=$(r,a),t=f(r,a),{label:s,value:t,index:e}})}var u={},i=angular.extend({},n,t);u.$values=[];var o,$,c,l,p,f,v;return u.init=function(){u.$match=o=s.match(i.regexp),$=r(o[2]||o[1]),c=o[4]||o[6],l=o[5],p=r(o[3]||""),f=r(o[2]?o[1]:c),v=r(o[7])},u.valuesFn=function(n,r){return e.when(v(n,r)).then(function(r){return u.$values=r?a(r,n):{},u.$values})},u.displayValue=function(n){var r={};return r[c]=n,$(r)},u.init(),u}return s}]});
//# sourceMappingURL=parse-options.min.js.map