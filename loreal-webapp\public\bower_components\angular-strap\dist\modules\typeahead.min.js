/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.typeahead",["mgcrea.ngStrap.tooltip","mgcrea.ngStrap.helpers.parseOptions"]).provider("$typeahead",function(){var e=this.defaults={animation:"am-fade",prefixClass:"typeahead",prefixEvent:"$typeahead",placement:"bottom-left",template:"typeahead/typeahead.tpl.html",trigger:"focus",container:!1,keyboard:!0,html:!1,delay:0,minLength:1,filter:"filter",limit:6,comparator:""};this.$get=["$window","$rootScope","$tooltip","$timeout",function(t,n,a,i){function o(t,n,o){var r={},l=angular.extend({},e,o);r=a(t,l);var c=o.scope,s=r.$scope;s.$resetMatches=function(){s.$matches=[],s.$activeIndex=0},s.$resetMatches(),s.$activate=function(e){s.$$postDigest(function(){r.activate(e)})},s.$select=function(e){s.$$postDigest(function(){r.select(e)})},s.$isVisible=function(){return r.$isVisible()},r.update=function(e){s.$matches=e,s.$activeIndex>=e.length&&(s.$activeIndex=0)},r.activate=function(e){s.$activeIndex=e},r.select=function(e){var t=s.$matches[e].value;n.$setViewValue(t),n.$render(),s.$resetMatches(),c&&c.$digest(),s.$emit(l.prefixEvent+".select",t,e,r)},r.$isVisible=function(){return l.minLength&&n?s.$matches.length&&angular.isString(n.$viewValue)&&n.$viewValue.length>=l.minLength:!!s.$matches.length},r.$getIndex=function(e){var t=s.$matches.length,n=t;if(t){for(n=t;n--&&s.$matches[n].value!==e;);if(!(0>n))return n}},r.$onMouseDown=function(e){e.preventDefault(),e.stopPropagation()},r.$onKeyDown=function(e){/(38|40|13)/.test(e.keyCode)&&(r.$isVisible()&&(e.preventDefault(),e.stopPropagation()),13===e.keyCode&&s.$matches.length?r.select(s.$activeIndex):38===e.keyCode&&s.$activeIndex>0?s.$activeIndex--:40===e.keyCode&&s.$activeIndex<s.$matches.length-1?s.$activeIndex++:angular.isUndefined(s.$activeIndex)&&(s.$activeIndex=0),s.$digest())};var u=r.show;r.show=function(){u(),i(function(){r.$element.on("mousedown",r.$onMouseDown),l.keyboard&&t.on("keydown",r.$onKeyDown)},0,!1)};var $=r.hide;return r.hide=function(){r.$element.off("mousedown",r.$onMouseDown),l.keyboard&&t.off("keydown",r.$onKeyDown),$()},r}angular.element(t.document.body);return o.defaults=e,o}]}).directive("bsTypeahead",["$window","$parse","$q","$typeahead","$parseOptions",function(e,t,n,a,i){var o=a.defaults;return{restrict:"EAC",require:"ngModel",link:function(e,t,n,r){var l={scope:e};angular.forEach(["placement","container","delay","trigger","keyboard","html","animation","template","filter","limit","minLength","watchOptions","selectMode","comparator","id"],function(e){angular.isDefined(n[e])&&(l[e]=n[e])});var c=l.filter||o.filter,s=l.limit||o.limit,u=l.comparator||o.comparator,$=n.ngOptions;c&&($+=" | "+c+":$viewValue"),u&&($+=":"+u),s&&($+=" | limitTo:"+s);var d=i($),f=a(t,r,l);if(l.watchOptions){var p=d.$match[7].replace(/\|.+/,"").replace(/\(.*\)/g,"").trim();e.$watch(p,function(){d.valuesFn(e,r).then(function(e){f.update(e),r.$render()})},!0)}e.$watch(n.ngModel,function(t){e.$modelValue=t,d.valuesFn(e,r).then(function(e){if(l.selectMode&&!e.length&&t.length>0)return void r.$setViewValue(r.$viewValue.substring(0,r.$viewValue.length-1));e.length>s&&(e=e.slice(0,s));var n=f.$isVisible();n&&f.update(e),(1!==e.length||e[0].value!==t)&&(!n&&f.update(e),r.$render())})}),r.$formatters.push(function(e){var t=d.displayValue(e);return void 0===t?"":t}),r.$render=function(){if(r.$isEmpty(r.$viewValue))return t.val("");var e=f.$getIndex(r.$modelValue),n=angular.isDefined(e)?f.$scope.$matches[e].label:r.$viewValue;n=angular.isObject(n)?d.displayValue(n):n,t.val(n?n.toString().replace(/<(?:.|\n)*?>/gm,"").trim():"")},e.$on("$destroy",function(){f&&f.destroy(),l=null,f=null})}}}]);
//# sourceMappingURL=typeahead.min.js.map