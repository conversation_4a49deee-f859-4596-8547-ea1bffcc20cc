/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.dropdown",["mgcrea.ngStrap.tooltip"]).provider("$dropdown",function(){var e=this.defaults={animation:"am-fade",prefixClass:"dropdown",placement:"bottom-left",template:"dropdown/dropdown.tpl.html",trigger:"click",container:!1,keyboard:!0,html:!1,delay:0};this.$get=["$window","$rootScope","$tooltip","$timeout",function(o,n,t,r){function a(o,a){function c(e){return e.target!==o[0]?e.target!==o[0]&&d.hide():void 0}{var d={},s=angular.extend({},e,a);d.$scope=s.scope&&s.scope.$new()||n.$new()}d=t(o,s);var p=o.parent();d.$onKeyDown=function(e){if(/(38|40)/.test(e.keyCode)){e.preventDefault(),e.stopPropagation();var o=angular.element(d.$element[0].querySelectorAll("li:not(.divider) a"));if(o.length){var n;angular.forEach(o,function(e,o){l&&l.call(e,":focus")&&(n=o)}),38===e.keyCode&&n>0?n--:40===e.keyCode&&n<o.length-1?n++:angular.isUndefined(n)&&(n=0),o.eq(n)[0].focus()}}};var u=d.show;d.show=function(){u(),r(function(){s.keyboard&&d.$element.on("keydown",d.$onKeyDown),i.on("click",c)},0,!1),p.hasClass("dropdown")&&p.addClass("open")};var f=d.hide;d.hide=function(){d.$isShown&&(s.keyboard&&d.$element.off("keydown",d.$onKeyDown),i.off("click",c),p.hasClass("dropdown")&&p.removeClass("open"),f())};var w=d.destroy;return d.destroy=function(){i.off("click",c),w()},d}var i=angular.element(o.document.body),l=Element.prototype.matchesSelector||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector;return a}]}).directive("bsDropdown",["$window","$sce","$dropdown",function(e,o,n){return{restrict:"EAC",scope:!0,link:function(e,o,t){var r={scope:e};angular.forEach(["placement","container","delay","trigger","keyboard","html","animation","template","id"],function(e){angular.isDefined(t[e])&&(r[e]=t[e])}),t.bsDropdown&&e.$watch(t.bsDropdown,function(o){e.content=o},!0),t.bsShow&&e.$watch(t.bsShow,function(e){a&&angular.isDefined(e)&&(angular.isString(e)&&(e=!!e.match(/true|,?(dropdown),?/i)),e===!0?a.show():a.hide())});var a=n(o,r);e.$on("$destroy",function(){a&&a.destroy(),r=null,a=null})}}}]);
//# sourceMappingURL=dropdown.min.js.map