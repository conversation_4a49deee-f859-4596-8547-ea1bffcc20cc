{"version": 3, "sources": ["typeahead/typeahead.js"], "names": [], "mappings": "qBASM,OAAA,4BAAa,yBAAA,iDAEb,aAAU,cAEV,GAAW,KAAA,UACX,UAAU,UACV,YAAM,YACN,YAAO,aACP,UAAW,cACX,SAAQ,+BACR,QAAO,QACP,WAAA,sBAGF,MAAK,cAEH,OAAI,iBAEJ,WAAS,sFAOP,GAAsB,EAAS,EAAA,GAE/B,GAAI,MAGF,EAAM,QAAW,UAAA,EAAA,WAGnB,IAAA,GAAM,EAAA,mBAGJ,cAAmB,aACjB,kDAKF,UAAM,SAAa,KACjB,aAAW,8BAKb,QAAO,SAAW,8CAMlB,WAAiB,WACjB,MAAG,GAAM,cAKX,EAAW,OAAA,SAAW,GACpB,EAAM,SAAA,kDAMN,SAAW,SAAc,GACzB,EAAA,aAAW,wBAIX,GAAA,GAAY,EAAA,SAAQ,GAAc,uDAKpC,GAAW,EAAa,YAEpB,MAAS,EAAM,YAAS,UAAA,EAAA,EAAA,4BAM5B,MAAA,GAAW,WAAY,EAIhB,EAAM,SAAS,QAAG,QAAiB,SAAA,EAAA,aAAA,EAAA,WAAA,QAAA,EAAA,YAHhC,EAAM,SAAS,UAMhB,UAAA,SAAA,qCAGT,IAAA,EAAA,EAAW,+BAGT,KAAI,EAAJ,gBAII,aAAkB,SAAI,sBAG1B,EAAG,2EAOD,EAAW,uDAMG,OAAX,SAAW,EAAY,SAAM,OAClC,EAAM,OAAA,EAAA,gEAKc,KAAlB,EAAO,SAAW,EAAA,aAAA,EAAA,SAAA,OAAA,EAAA,EAAA,eACtB,QAAkB,YAAW,EAAA,gBAAA,EAAA,aAAA,GAC3B,EAAA,eAKE,GAAG,EAAQ,OACT,KAAW,4BAKb,EAAO,SAAW,GAAA,YAAA,EAAA,cACtB,EAAW,UACT,EAAW,GAAA,UAAa,EAAa,aAEnC,GAAA,0CAKJ,EAAO,SAAA,IAAA,YAAA,EAAA,wDAIT,OA/HM,QAAU,QAAQ,EAAW,SAAU,YAsIhD,GAAU,SAAA,kBAOD,eAAS,UAAgB,SAAS,KAAM,aAAY,gBAAA,SAAA,EAAA,EAAA,EAAA,EAAA,oCAKnD,+CAKL,GAAI,IAAQ,MAAQ,EACpB,SAAI,SAAa,YAAQ,YAAc,QAAS,UAAA,WAAA,OAAA,YAAA,WAAA,SAAA,QAAA,YAAA,eAAA,aAAA,aAAA,MAAA,SAAA,yCAKhD,IAAG,GAAO,EAAA,QAAa,EAAgB,OACnC,EAAA,EAAgB,OAAA,EAAc,mCAG9B,EAAY,EAAA,sDAGb,IAAA,GAAQ,cAAc,8BAOnB,aAAW,IAEZ,GAAA,EAAA,OAAA,GAAA,QAAA,OAAA,IAAA,QAAA,UAAA,IAAA,8DAIC,EAAO,OAAK,mBAGhB,KAIE,OAAG,EAAQ,QAAA,SAAe,KAExB,YAAA,uBAEC,SAAO,0CAKT,WADD,GAAU,cAAgB,EAAU,WAAU,UAAU,EAAA,EAAA,WAAA,OAAA,GAGxD,GAAA,OAAW,IAAA,EAAA,EAAA,MAAA,EAAA,0EAKf,GAAW,EAAiB,OAAA,GAE1B,EAAI,eAKN,EAAW,YAAU,KAAA,SAAY,GAE/B,GAAG,GAAW,EAAS,aAAW,EAClC,OAAsB,UAAlB,EAA4B,GAAA,qEAOlC,IAAM,GAAI,EAAY,UAAW,EAAA,aAC3B,EAAW,QAAA,UAAU,GAAA,EAAA,OAAA,SAAA,GAAA,MAAA,EAAA,UACzB,GAAU,QAAA,SAAA,GAAA,EAAA,aAAA,GAAA,EACV,EAAA,IAAY,EAAA,EAAA,WAAA,QAAA,iBAAA,IAAA,OAAA,gDAMjB,EAAA", "file": "typeahead.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.typeahead', ['mgcrea.ngStrap.tooltip', 'mgcrea.ngStrap.helpers.parseOptions'])\n\n  .provider('$typeahead', function() {\n\n    var defaults = this.defaults = {\n      animation: 'am-fade',\n      prefixClass: 'typeahead',\n      prefixEvent: '$typeahead',\n      placement: 'bottom-left',\n      template: 'typeahead/typeahead.tpl.html',\n      trigger: 'focus',\n      container: false,\n      keyboard: true,\n      html: false,\n      delay: 0,\n      minLength: 1,\n      filter: 'filter',\n      limit: 6,\n      comparator: ''\n    };\n\n    this.$get = function($window, $rootScope, $tooltip, $timeout) {\n\n      var bodyEl = angular.element($window.document.body);\n\n      function TypeaheadFactory(element, controller, config) {\n\n        var $typeahead = {};\n\n        // Common vars\n        var options = angular.extend({}, defaults, config);\n\n        $typeahead = $tooltip(element, options);\n        var parentScope = config.scope;\n        var scope = $typeahead.$scope;\n\n        scope.$resetMatches = function(){\n          scope.$matches = [];\n          scope.$activeIndex = 0;\n        };\n        scope.$resetMatches();\n\n        scope.$activate = function(index) {\n          scope.$$postDigest(function() {\n            $typeahead.activate(index);\n          });\n        };\n\n        scope.$select = function(index, evt) {\n          scope.$$postDigest(function() {\n            $typeahead.select(index);\n          });\n        };\n\n        scope.$isVisible = function() {\n          return $typeahead.$isVisible();\n        };\n\n        // Public methods\n\n        $typeahead.update = function(matches) {\n          scope.$matches = matches;\n          if(scope.$activeIndex >= matches.length) {\n            scope.$activeIndex = 0;\n          }\n        };\n\n        $typeahead.activate = function(index) {\n          scope.$activeIndex = index;\n        };\n\n        $typeahead.select = function(index) {\n          var value = scope.$matches[index].value;\n          // console.log('$setViewValue', value);\n          controller.$setViewValue(value);\n          controller.$render();\n          scope.$resetMatches();\n          if(parentScope) parentScope.$digest();\n          // Emit event\n          scope.$emit(options.prefixEvent + '.select', value, index, $typeahead);\n        };\n\n        // Protected methods\n\n        $typeahead.$isVisible = function() {\n          if(!options.minLength || !controller) {\n            return !!scope.$matches.length;\n          }\n          // minLength support\n          return scope.$matches.length && angular.isString(controller.$viewValue) && controller.$viewValue.length >= options.minLength;\n        };\n\n        $typeahead.$getIndex = function(value) {\n          var l = scope.$matches.length, i = l;\n          if(!l) return;\n          for(i = l; i--;) {\n            if(scope.$matches[i].value === value) break;\n          }\n          if(i < 0) return;\n          return i;\n        };\n\n        $typeahead.$onMouseDown = function(evt) {\n          // Prevent blur on mousedown\n          evt.preventDefault();\n          evt.stopPropagation();\n        };\n\n        $typeahead.$onKeyDown = function(evt) {\n          if(!/(38|40|13)/.test(evt.keyCode)) return;\n\n          // Let ngSubmit pass if the typeahead tip is hidden\n          if($typeahead.$isVisible()) {\n            evt.preventDefault();\n            evt.stopPropagation();\n          }\n\n          // Select with enter\n          if(evt.keyCode === 13 && scope.$matches.length) {\n            $typeahead.select(scope.$activeIndex);\n          }\n\n          // Navigate with keyboard\n          else if(evt.keyCode === 38 && scope.$activeIndex > 0) scope.$activeIndex--;\n          else if(evt.keyCode === 40 && scope.$activeIndex < scope.$matches.length - 1) scope.$activeIndex++;\n          else if(angular.isUndefined(scope.$activeIndex)) scope.$activeIndex = 0;\n          scope.$digest();\n        };\n\n        // Overrides\n\n        var show = $typeahead.show;\n        $typeahead.show = function() {\n          show();\n          // use timeout to hookup the events to prevent\n          // event bubbling from being processed imediately.\n          $timeout(function() {\n            $typeahead.$element.on('mousedown', $typeahead.$onMouseDown);\n            if(options.keyboard) {\n              element.on('keydown', $typeahead.$onKeyDown);\n            }\n          }, 0, false);\n        };\n\n        var hide = $typeahead.hide;\n        $typeahead.hide = function() {\n          $typeahead.$element.off('mousedown', $typeahead.$onMouseDown);\n          if(options.keyboard) {\n            element.off('keydown', $typeahead.$onKeyDown);\n          }\n          hide();\n        };\n\n        return $typeahead;\n\n      }\n\n      TypeaheadFactory.defaults = defaults;\n      return TypeaheadFactory;\n\n    };\n\n  })\n\n  .directive('bsTypeahead', function($window, $parse, $q, $typeahead, $parseOptions) {\n\n    var defaults = $typeahead.defaults;\n\n    return {\n      restrict: 'EAC',\n      require: 'ngModel',\n      link: function postLink(scope, element, attr, controller) {\n\n        // Directive options\n        var options = {scope: scope};\n        angular.forEach(['placement', 'container', 'delay', 'trigger', 'keyboard', 'html', 'animation', 'template', 'filter', 'limit', 'minLength', 'watchOptions', 'selectMode', 'comparator', 'id'], function(key) {\n          if(angular.isDefined(attr[key])) options[key] = attr[key];\n        });\n\n        // Build proper ngOptions\n        var filter = options.filter || defaults.filter;\n        var limit = options.limit || defaults.limit;\n        var comparator = options.comparator || defaults.comparator;\n\n        var ngOptions = attr.ngOptions;\n        if(filter) ngOptions += ' | ' + filter + ':$viewValue';\n        if (comparator) ngOptions += ':' + comparator;\n        if(limit) ngOptions += ' | limitTo:' + limit;\n        var parsedOptions = $parseOptions(ngOptions);\n\n        // Initialize typeahead\n        var typeahead = $typeahead(element, controller, options);\n\n        // Watch options on demand\n        if(options.watchOptions) {\n          // Watch ngOptions values before filtering for changes, drop function calls\n          var watchedOptions = parsedOptions.$match[7].replace(/\\|.+/, '').replace(/\\(.*\\)/g, '').trim();\n          scope.$watch(watchedOptions, function (newValue, oldValue) {\n            // console.warn('scope.$watch(%s)', watchedOptions, newValue, oldValue);\n            parsedOptions.valuesFn(scope, controller).then(function (values) {\n              typeahead.update(values);\n              controller.$render();\n            });\n          }, true);\n        }\n\n        // Watch model for changes\n        scope.$watch(attr.ngModel, function(newValue, oldValue) {\n          // console.warn('$watch', element.attr('ng-model'), newValue);\n          scope.$modelValue = newValue; // Publish modelValue on scope for custom templates\n          parsedOptions.valuesFn(scope, controller)\n          .then(function(values) {\n            // Prevent input with no future prospect if selectMode is truthy\n            // @TODO test selectMode\n            if(options.selectMode && !values.length && newValue.length > 0) {\n              controller.$setViewValue(controller.$viewValue.substring(0, controller.$viewValue.length - 1));\n              return;\n            }\n            if(values.length > limit) values = values.slice(0, limit);\n            var isVisible = typeahead.$isVisible();\n            isVisible && typeahead.update(values);\n            // Do not re-queue an update if a correct value has been selected\n            if(values.length === 1 && values[0].value === newValue) return;\n            !isVisible && typeahead.update(values);\n            // Queue a new rendering that will leverage collection loading\n            controller.$render();\n          });\n        });\n\n        // modelValue -> $formatters -> viewValue\n        controller.$formatters.push(function(modelValue) {\n          // console.warn('$formatter(\"%s\"): modelValue=%o (%o)', element.attr('ng-model'), modelValue, typeof modelValue);\n          var displayValue = parsedOptions.displayValue(modelValue);\n          return displayValue === undefined ? '' : displayValue;\n        });\n\n        // Model rendering in view\n        controller.$render = function () {\n          // console.warn('$render', element.attr('ng-model'), 'controller.$modelValue', typeof controller.$modelValue, controller.$modelValue, 'controller.$viewValue', typeof controller.$viewValue, controller.$viewValue);\n          if(controller.$isEmpty(controller.$viewValue)) return element.val('');\n          var index = typeahead.$getIndex(controller.$modelValue);\n          var selected = angular.isDefined(index) ? typeahead.$scope.$matches[index].label : controller.$viewValue;\n          selected = angular.isObject(selected) ? parsedOptions.displayValue(selected) : selected;\n          element.val(selected ? selected.toString().replace(/<(?:.|\\n)*?>/gm, '').trim() : '');\n        };\n\n        // Garbage collection\n        scope.$on('$destroy', function() {\n          if (typeahead) typeahead.destroy();\n          options = null;\n          typeahead = null;\n        });\n\n      }\n    };\n\n  });\n"], "sourceRoot": "/source/"}