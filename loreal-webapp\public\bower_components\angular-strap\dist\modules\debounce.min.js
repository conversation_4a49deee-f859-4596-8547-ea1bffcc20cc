/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.helpers.debounce",[]).factory("debounce",["$timeout",function(n){return function(t,u,r){var e=null;return function(){var a=this,l=arguments,c=r&&!e;return e&&n.cancel(e),e=n(function(){e=null,r||t.apply(a,l)},u,!1),c&&t.apply(a,l),e}}}]).factory("throttle",["$timeout",function(n){return function(t,u,r){var e=null;return r||(r={}),function(){var a=this,l=arguments;e||(r.leading!==!1&&t.apply(a,l),e=n(function(){e=null,r.trailing!==!1&&t.apply(a,l)},u,!1))}}}]);
//# sourceMappingURL=debounce.min.js.map