{"version": 3, "sources": ["es5-sham.js"], "names": ["root", "factory", "define", "amd", "exports", "module", "returnExports", "this", "call", "Function", "prototype", "prototypeOfObject", "Object", "owns", "bind", "hasOwnProperty", "defineGetter", "defineSetter", "lookupGetter", "lookupSetter", "supportsAccessors", "__defineGetter__", "__defineSetter__", "__lookupGetter__", "__lookupSetter__", "getPrototypeOf", "object", "proto", "__proto__", "constructor", "doesGetOwnPropertyDescriptorWork", "sentinel", "getOwnPropertyDescriptor", "value", "exception", "defineProperty", "getOwnPropertyDescriptorWorksOnObject", "getOwnPropertyDescriptorWorksOnDom", "document", "createElement", "getOwnPropertyDescriptorFallback", "ERR_NON_OBJECT", "property", "TypeError", "descriptor", "enumerable", "configurable", "notPrototypeOfObject", "getter", "setter", "get", "set", "writable", "getOwnPropertyNames", "keys", "create", "createEmpty", "supportsProto", "iframe", "parent", "body", "documentElement", "style", "display", "append<PERSON><PERSON><PERSON>", "src", "empty", "contentWindow", "<PERSON><PERSON><PERSON><PERSON>", "propertyIsEnumerable", "isPrototypeOf", "toLocaleString", "toString", "valueOf", "Empty", "properties", "Type", "defineProperties", "doesDefinePropertyWork", "definePropertyWorksOnObject", "definePropertyWorksOnDom", "definePropertyFallback", "definePropertiesFallback", "ERR_NON_OBJECT_DESCRIPTOR", "ERR_NON_OBJECT_TARGET", "ERR_ACCESSORS_NOT_SUPPORTED", "seal", "freeze", "freezeObject", "preventExtensions", "isSealed", "isFrozen", "isExtensible", "name", "returnValue"], "mappings": ";;;;;CAaC,SAAUA,EAAMC,GACb,YAEA,UAAWC,UAAW,YAAcA,OAAOC,IAAK,CAE5CD,OAAOD,OACJ,UAAWG,WAAY,SAAU,CAIpCC,OAAOD,QAAUH,QACd,CAEHD,EAAKM,cAAgBL,OAE3BM,KAAM,WAER,GAAIC,GAAOC,SAASC,UAAUF,IAC9B,IAAIG,GAAoBC,OAAOF,SAC/B,IAAIG,GAAOL,EAAKM,KAAKH,EAAkBI,eAGvC,IAAIC,EACJ,IAAIC,EACJ,IAAIC,EACJ,IAAIC,EACJ,IAAIC,GAAoBP,EAAKF,EAAmB,mBAChD,IAAIS,EAAmB,CACnBJ,EAAeR,EAAKM,KAAKH,EAAkBU,iBAC3CJ,GAAeT,EAAKM,KAAKH,EAAkBW,iBAC3CJ,GAAeV,EAAKM,KAAKH,EAAkBY,iBAC3CJ,GAAeX,EAAKM,KAAKH,EAAkBa,kBAK/C,IAAKZ,OAAOa,eAAgB,CAQxBb,OAAOa,eAAiB,QAASA,GAAeC,GAC5C,GAAIC,GAAQD,EAAOE,SACnB,IAAID,GAASA,IAAU,KAAM,CACzB,MAAOA,OACJ,IAAID,EAAOG,YAAa,CAC3B,MAAOH,GAAOG,YAAYnB,cACvB,CACH,MAAOC,KAQnB,QAASmB,GAAiCJ,GACtC,IACIA,EAAOK,SAAW,CAClB,OAAOnB,QAAOoB,yBAAyBN,EAAQ,YAAYO,QAAU,EACvE,MAAOC,KAOb,GAAItB,OAAOuB,eAAgB,CACvB,GAAIC,GAAwCN,KAC5C,IAAIO,SAA4CC,YAAa,aAC7DR,EAAiCQ,SAASC,cAAc,OACxD,KAAKF,IAAuCD,EAAuC,CAC/E,GAAII,GAAmC5B,OAAOoB,0BAItD,IAAKpB,OAAOoB,0BAA4BQ,EAAkC,CACtE,GAAIC,GAAiB,0DAErB7B,QAAOoB,yBAA2B,QAASA,GAAyBN,EAAQgB,GACxE,SAAYhB,KAAW,gBAAmBA,KAAW,YAAeA,IAAW,KAAM,CACjF,KAAM,IAAIiB,WAAUF,EAAiBf,GAKzC,GAAIc,EAAkC,CAClC,IACI,MAAOA,GAAiChC,KAAKI,OAAQc,EAAQgB,GAC/D,MAAOR,KAMb,IAAKrB,EAAKa,EAAQgB,GAAW,CACzB,OAKJ,GAAIE,IAAeC,WAAY,KAAMC,aAAc,KAInD,IAAI1B,EAAmB,CAMnB,GAAIV,GAAYgB,EAAOE,SACvB,IAAImB,GAAuBrB,IAAWf,CAItC,IAAIoC,EAAsB,CACtBrB,EAAOE,UAAYjB,EAGvB,GAAIqC,GAAS9B,EAAaQ,EAAQgB,EAClC,IAAIO,GAAS9B,EAAaO,EAAQgB,EAElC,IAAIK,EAAsB,CAEtBrB,EAAOE,UAAYlB,EAGvB,GAAIsC,GAAUC,EAAQ,CAClB,GAAID,EAAQ,CACRJ,EAAWM,IAAMF,EAErB,GAAIC,EAAQ,CACRL,EAAWO,IAAMF,EAIrB,MAAOL,IAMfA,EAAWX,MAAQP,EAAOgB,EAC1BE,GAAWQ,SAAW,IACtB,OAAOR,IAMf,IAAKhC,OAAOyC,oBAAqB,CAC7BzC,OAAOyC,oBAAsB,QAASA,GAAoB3B,GACtD,MAAOd,QAAO0C,KAAK5B,IAM3B,IAAKd,OAAO2C,OAAQ,CAGhB,GAAIC,EACJ,IAAIC,MAAoB7B,UAAW,eAAkBhB,QAKrD,IAAI6C,SAAwBnB,YAAa,YAAa,CAClDkB,EAAc,WACV,OAAS5B,UAAW,WAErB,CAMH4B,EAAc,WACV,GAAIE,GAASpB,SAASC,cAAc,SACpC,IAAIoB,GAASrB,SAASsB,MAAQtB,SAASuB,eACvCH,GAAOI,MAAMC,QAAU,MACvBJ,GAAOK,YAAYN,EACnBA,GAAOO,IAAM,aACb,IAAIC,GAAQR,EAAOS,cAAcvD,OAAOF,SACxCiD,GAAOS,YAAYV,EACnBA,GAAS,WACFQ,GAAMrC,kBACNqC,GAAMnD,qBACNmD,GAAMG,2BACNH,GAAMI,oBACNJ,GAAMK,qBACNL,GAAMM,eACNN,GAAMO,OACbP,GAAMtC,UAAY,IAElB,SAAS8C,MACTA,EAAMhE,UAAYwD,CAElBV,GAAc,WACV,MAAO,IAAIkB,GAEf,OAAO,IAAIA,IAInB9D,OAAO2C,OAAS,QAASA,GAAO7C,EAAWiE,GAEvC,GAAIjD,EACJ,SAASkD,MAET,GAAIlE,IAAc,KAAM,CACpBgB,EAAS8B,QACN,CACH,SAAW9C,KAAc,gBAAmBA,KAAc,WAAY,CAMlE,KAAM,IAAIiC,WAAU,kDAExBiC,EAAKlE,UAAYA,CACjBgB,GAAS,GAAIkD,EAKblD,GAAOE,UAAYlB,EAGvB,GAAIiE,QAAoB,GAAG,CACvB/D,OAAOiE,iBAAiBnD,EAAQiD,GAGpC,MAAOjD,IAgBf,QAASoD,GAAuBpD,GAC5B,IACId,OAAOuB,eAAeT,EAAQ,cAC9B,OAAO,YAAcA,GACvB,MAAOQ,KAOb,GAAItB,OAAOuB,eAAgB,CACvB,GAAI4C,GAA8BD,KAClC,IAAIE,SAAkC1C,YAAa,aAC/CwC,EAAuBxC,SAASC,cAAc,OAClD,KAAKwC,IAAgCC,EAA0B,CAC3D,GAAIC,GAAyBrE,OAAOuB,eAChC+C,EAA2BtE,OAAOiE,kBAI9C,IAAKjE,OAAOuB,gBAAkB8C,EAAwB,CAClD,GAAIE,GAA4B,0CAChC,IAAIC,GAAwB,8CAC5B,IAAIC,GAA8B,gEAElCzE,QAAOuB,eAAiB,QAASA,GAAeT,EAAQgB,EAAUE,GAC9D,SAAYlB,KAAW,gBAAmBA,KAAW,YAAeA,IAAW,KAAM,CACjF,KAAM,IAAIiB,WAAUyC,EAAwB1D,GAEhD,SAAYkB,KAAe,gBAAmBA,KAAe,YAAeA,IAAe,KAAM,CAC7F,KAAM,IAAID,WAAUwC,EAA4BvC,GAIpD,GAAIqC,EAAwB,CACxB,IACI,MAAOA,GAAuBzE,KAAKI,OAAQc,EAAQgB,EAAUE,GAC/D,MAAOV,KAMb,GAAIrB,EAAK+B,EAAY,SAAU,CAe3B,GAAIxB,IAAsBF,EAAaQ,EAAQgB,IAAavB,EAAaO,EAAQgB,IAAY,CAKzF,GAAIhC,GAAYgB,EAAOE,SACvBF,GAAOE,UAAYjB,QAGZe,GAAOgB,EACdhB,GAAOgB,GAAYE,EAAWX,KAE9BP,GAAOE,UAAYlB,MAChB,CACHgB,EAAOgB,GAAYE,EAAWX,WAE/B,CACH,IAAKb,EAAmB,CACpB,KAAM,IAAIuB,WAAU0C,GAGxB,GAAIxE,EAAK+B,EAAY,OAAQ,CACzB5B,EAAaU,EAAQgB,EAAUE,EAAWM,KAE9C,GAAIrC,EAAK+B,EAAY,OAAQ,CACzB3B,EAAaS,EAAQgB,EAAUE,EAAWO,MAGlD,MAAOzB,IAMf,IAAKd,OAAOiE,kBAAoBK,EAA0B,CACtDtE,OAAOiE,iBAAmB,QAASA,GAAiBnD,EAAQiD,GAExD,GAAIO,EAA0B,CAC1B,IACI,MAAOA,GAAyB1E,KAAKI,OAAQc,EAAQiD,GACvD,MAAOzC,KAKb,IAAK,GAAIQ,KAAYiC,GAAY,CAC7B,GAAI9D,EAAK8D,EAAYjC,IAAaA,IAAa,YAAa,CACxD9B,OAAOuB,eAAeT,EAAQgB,EAAUiC,EAAWjC,KAG3D,MAAOhB,IAMf,IAAKd,OAAO0E,KAAM,CACd1E,OAAO0E,KAAO,QAASA,GAAK5D,GAIxB,MAAOA,IAMf,IAAKd,OAAO2E,OAAQ,CAChB3E,OAAO2E,OAAS,QAASA,GAAO7D,GAI5B,MAAOA,IAKf,IACId,OAAO2E,OAAO,cAChB,MAAOrD,GACLtB,OAAO2E,OAAU,QAASA,GAAOC,GAC7B,MAAO,SAASD,GAAO7D,GACnB,SAAWA,KAAW,WAAY,CAC9B,MAAOA,OACJ,CACH,MAAO8D,GAAa9D,MAG9Bd,OAAO2E,QAKb,IAAK3E,OAAO6E,kBAAmB,CAC3B7E,OAAO6E,kBAAoB,QAASA,GAAkB/D,GAIlD,MAAOA,IAMf,IAAKd,OAAO8E,SAAU,CAClB9E,OAAO8E,SAAW,QAASA,GAAShE,GAChC,MAAO,QAMf,IAAKd,OAAO+E,SAAU,CAClB/E,OAAO+E,SAAW,QAASA,GAASjE,GAChC,MAAO,QAMf,IAAKd,OAAOgF,aAAc,CACtBhF,OAAOgF,aAAe,QAASA,GAAalE,GAExC,GAAId,OAAOc,KAAYA,EAAQ,CAC3B,KAAM,IAAIiB,WAGd,GAAIkD,GAAO,EACX,OAAOhF,EAAKa,EAAQmE,GAAO,CACvBA,GAAQ,IAEZnE,EAAOmE,GAAQ,IACf,IAAIC,GAAcjF,EAAKa,EAAQmE,SACxBnE,GAAOmE,EACd,OAAOC"}