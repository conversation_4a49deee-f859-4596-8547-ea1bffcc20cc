{"version": 3, "sources": ["dropdown/dropdown.js"], "names": [], "mappings": "qBASM,OAAA,2BAAW,oCAEX,YAAS,cAET,GAAU,KAAA,UACV,UAAM,UACN,YAAO,yEAGT,QAAK,qBAEH,UAAI,EACJ,MAAI,gBAIF,MAAI,UAAY,aAAA,WAAA,WAAA,SAAA,EAAA,EAAA,EAAA,wDAwEX,EAAA,SAAA,EAAA,IAAA,EAAA,cAjEL,CAAA,GAAI,8BAIM,GAAA,OAAa,EAAc,OAAA,EAAA,MAAA,QAAA,EAAA,SAE/B,EAAA,EAAA,MACJ,GAAI,EAAA,WAIA,WAAc,SAAA,GAClB,GAAI,UAAA,KAAA,EAAA,SAAJ,CACA,EAAA,mBACE,6FAIF,IAAG,EAAI,OAAP,eAEQ,QAAQ,EAAA,SAAY,EAAQ,GACpC,GAAmB,EAAA,KAAA,EAAA,YAAA,EAAA,8DAMjB,QAAO,YAAU,KAAA,EAAA,GACrB,EAAA,GAAU,GAAO,GAAA,cAMb,GAAO,EAAG,OACN,KAAA,WACN,IAGF,EAAW,WACX,EAAU,UAAO,EAAW,SAAA,GAAA,UAAA,EAAA,YAC1B,EAAI,GAAA,QAAU,IACd,GAAA,GACA,EAAO,SAAI,aAAS,EAAA,SAAA,wCAKlB,EAAU,WACd,EAAU,UAAU,EAAW,SAAA,IAAA,UAAA,EAAA,YAC7B,EAAO,IAAI,QAAS,GACpB,EAAA,SAAA,aAAA,EAAA,YAAA,oCAKF,GAAS,QAAA,WACP,EAAO,IAAA,QAAW,GAClB,8CA9DF,EAAc,QAAQ,UAAW,iBAAU,QAAA,UAAA,uBAAA,QAAA,UAAA,oBAAA,QAAA,UAAA,mBAAA,QAAA,UAAA,mHAqFtC,uCAKH,IAAM,MAAU,WACf,SAAA,YAAA,YAAA,QAAA,UAAA,WAAA,OAAA,YAAA,WAAA,MAAA,SAAA,4CAKE,YAAQ,EAAS,OAAA,EAAW,WAAa,SAAS,GACrD,EAAA,QAAa,OAIf,EAAI,QAAW,EAAA,OAAU,EAAA,OAAS,SAAA,wFAGlC,KAAU,EAAY,EAAA,OAAW,EAAA,kEASpC,EAAA", "file": "dropdown.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.dropdown', ['mgcrea.ngStrap.tooltip'])\n\n  .provider('$dropdown', function() {\n\n    var defaults = this.defaults = {\n      animation: 'am-fade',\n      prefixClass: 'dropdown',\n      placement: 'bottom-left',\n      template: 'dropdown/dropdown.tpl.html',\n      trigger: 'click',\n      container: false,\n      keyboard: true,\n      html: false,\n      delay: 0\n    };\n\n    this.$get = function($window, $rootScope, $tooltip, $timeout) {\n\n      var bodyEl = angular.element($window.document.body);\n      var matchesSelector = Element.prototype.matchesSelector || Element.prototype.webkitMatchesSelector || Element.prototype.mozMatchesSelector || Element.prototype.msMatchesSelector || Element.prototype.oMatchesSelector;\n\n      function DropdownFactory(element, config) {\n\n        var $dropdown = {};\n\n        // Common vars\n        var options = angular.extend({}, defaults, config);\n        var scope = $dropdown.$scope = options.scope && options.scope.$new() || $rootScope.$new();\n\n        $dropdown = $tooltip(element, options);\n        var parentEl = element.parent();\n\n        // Protected methods\n\n        $dropdown.$onKeyDown = function(evt) {\n          if (!/(38|40)/.test(evt.keyCode)) return;\n          evt.preventDefault();\n          evt.stopPropagation();\n\n          // Retrieve focused index\n          var items = angular.element($dropdown.$element[0].querySelectorAll('li:not(.divider) a'));\n          if(!items.length) return;\n          var index;\n          angular.forEach(items, function(el, i) {\n            if(matchesSelector && matchesSelector.call(el, ':focus')) index = i;\n          });\n\n          // Navigate with keyboard\n          if(evt.keyCode === 38 && index > 0) index--;\n          else if(evt.keyCode === 40 && index < items.length - 1) index++;\n          else if(angular.isUndefined(index)) index = 0;\n          items.eq(index)[0].focus();\n\n        };\n\n        // Overrides\n\n        var show = $dropdown.show;\n        $dropdown.show = function() {\n          show();\n          // use timeout to hookup the events to prevent\n          // event bubbling from being processed imediately.\n          $timeout(function() {\n            options.keyboard && $dropdown.$element.on('keydown', $dropdown.$onKeyDown);\n            bodyEl.on('click', onBodyClick);\n          }, 0, false);\n          parentEl.hasClass('dropdown') && parentEl.addClass('open');\n        };\n\n        var hide = $dropdown.hide;\n        $dropdown.hide = function() {\n          if(!$dropdown.$isShown) return;\n          options.keyboard && $dropdown.$element.off('keydown', $dropdown.$onKeyDown);\n          bodyEl.off('click', onBodyClick);\n          parentEl.hasClass('dropdown') && parentEl.removeClass('open');\n          hide();\n        };\n\n        var destroy = $dropdown.destroy;\n        $dropdown.destroy = function() {\n          bodyEl.off('click', onBodyClick);\n          destroy();\n        };\n\n        // Private functions\n\n        function onBodyClick(evt) {\n          if(evt.target === element[0]) return;\n          return evt.target !== element[0] && $dropdown.hide();\n        }\n\n        return $dropdown;\n\n      }\n\n      return DropdownFactory;\n\n    };\n\n  })\n\n  .directive('bsDropdown', function($window, $sce, $dropdown) {\n\n    return {\n      restrict: 'EAC',\n      scope: true,\n      link: function postLink(scope, element, attr, transclusion) {\n\n        // Directive options\n        var options = {scope: scope};\n        angular.forEach(['placement', 'container', 'delay', 'trigger', 'keyboard', 'html', 'animation', 'template', 'id'], function(key) {\n          if(angular.isDefined(attr[key])) options[key] = attr[key];\n        });\n\n        // Support scope as an object\n        attr.bsDropdown && scope.$watch(attr.bsDropdown, function(newValue, oldValue) {\n          scope.content = newValue;\n        }, true);\n\n        // Visibility binding support\n        attr.bsShow && scope.$watch(attr.bsShow, function(newValue, oldValue) {\n          if(!dropdown || !angular.isDefined(newValue)) return;\n          if(angular.isString(newValue)) newValue = !!newValue.match(/true|,?(dropdown),?/i);\n          newValue === true ? dropdown.show() : dropdown.hide();\n        });\n\n        // Initialize dropdown\n        var dropdown = $dropdown(element, options);\n\n        // Garbage collection\n        scope.$on('$destroy', function() {\n          if (dropdown) dropdown.destroy();\n          options = null;\n          dropdown = null;\n        });\n\n      }\n    };\n\n  });\n"], "sourceRoot": "/source/"}