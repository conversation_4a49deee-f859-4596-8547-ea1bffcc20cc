/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.helpers.dateParser",[]).provider("$dateParser",["$localeProvider",function(){function t(){this.year=1970,this.month=0,this.day=1,this.hours=0,this.minutes=0,this.seconds=0,this.milliseconds=0}function e(){}function s(t){return!isNaN(parseFloat(t))&&isFinite(t)}function n(t,e){for(var s=t.length,n=e.toString().toLowerCase(),r=0;s>r;r++)if(t[r].toLowerCase()===n)return r;return-1}t.prototype.setMilliseconds=function(t){this.milliseconds=t},t.prototype.setSeconds=function(t){this.seconds=t},t.prototype.setMinutes=function(t){this.minutes=t},t.prototype.setHours=function(t){this.hours=t},t.prototype.getHours=function(){return this.hours},t.prototype.setDate=function(t){this.day=t},t.prototype.setMonth=function(t){this.month=t},t.prototype.setFullYear=function(t){this.year=t},t.prototype.fromDate=function(t){return this.year=t.getFullYear(),this.month=t.getMonth(),this.day=t.getDate(),this.hours=t.getHours(),this.minutes=t.getMinutes(),this.seconds=t.getSeconds(),this.milliseconds=t.getMilliseconds(),this},t.prototype.toDate=function(){return new Date(this.year,this.month,this.day,this.hours,this.minutes,this.seconds,this.milliseconds)};var r=t.prototype,i=this.defaults={format:"shortDate",strict:!1};this.$get=["$locale","dateFilter",function(o,a){var u=function(u){function h(t){var e,s=Object.keys(d),n=[],r=[],i=t;for(e=0;e<s.length;e++)if(t.split(s[e]).length>1){var o=i.search(s[e]);t=t.split(s[e]).join(""),d[s[e]]&&(n[o]=d[s[e]])}return angular.forEach(n,function(t){t&&r.push(t)}),r}function l(t){return t.replace(/\//g,"[\\/]").replace("/-/g","[-]").replace(/\./g,"[.]").replace(/\\s/g,"[\\s]")}function c(t){var e,s=Object.keys(m),n=t;for(e=0;e<s.length;e++)n=n.split(s[e]).join("${"+e+"}");for(e=0;e<s.length;e++)n=n.split("${"+e+"}").join("("+m[s[e]]+")");return t=l(t),new RegExp("^"+n+"$",["i"])}var M,f,g=angular.extend({},i,u),p={},m={sss:"[0-9]{3}",ss:"[0-5][0-9]",s:g.strict?"[1-5]?[0-9]":"[0-9]|[0-5][0-9]",mm:"[0-5][0-9]",m:g.strict?"[1-5]?[0-9]":"[0-9]|[0-5][0-9]",HH:"[01][0-9]|2[0-3]",H:g.strict?"1?[0-9]|2[0-3]":"[01]?[0-9]|2[0-3]",hh:"[0][1-9]|[1][012]",h:g.strict?"[1-9]|1[012]":"0?[1-9]|1[012]",a:"AM|PM",EEEE:o.DATETIME_FORMATS.DAY.join("|"),EEE:o.DATETIME_FORMATS.SHORTDAY.join("|"),dd:"0[1-9]|[12][0-9]|3[01]",d:g.strict?"[1-9]|[1-2][0-9]|3[01]":"0?[1-9]|[1-2][0-9]|3[01]",MMMM:o.DATETIME_FORMATS.MONTH.join("|"),MMM:o.DATETIME_FORMATS.SHORTMONTH.join("|"),MM:"0[1-9]|1[012]",M:g.strict?"[1-9]|1[012]":"0?[1-9]|1[012]",yyyy:"[1]{1}[0-9]{3}|[2]{1}[0-9]{3}",yy:"[0-9]{2}",y:g.strict?"-?(0|[1-9][0-9]{0,3})":"-?0*[0-9]{1,4}"},d={sss:r.setMilliseconds,ss:r.setSeconds,s:r.setSeconds,mm:r.setMinutes,m:r.setMinutes,HH:r.setHours,H:r.setHours,hh:r.setHours,h:r.setHours,EEEE:e,EEE:e,dd:r.setDate,d:r.setDate,a:function(t){var e=this.getHours()%12;return this.setHours(t.match(/pm/i)?e+12:e)},MMMM:function(t){return this.setMonth(n(o.DATETIME_FORMATS.MONTH,t))},MMM:function(t){return this.setMonth(n(o.DATETIME_FORMATS.SHORTMONTH,t))},MM:function(t){return this.setMonth(1*t-1)},M:function(t){return this.setMonth(1*t-1)},yyyy:r.setFullYear,yy:function(t){return this.setFullYear(2e3+1*t)},y:r.setFullYear};return p.init=function(){p.$format=o.DATETIME_FORMATS[g.format]||g.format,M=c(p.$format),f=h(p.$format)},p.isValid=function(t){return angular.isDate(t)?!isNaN(t.getTime()):M.test(t)},p.parse=function(e,s,n){n&&(n=o.DATETIME_FORMATS[n]||n),angular.isDate(e)&&(e=a(e,n||p.$format));var r=n?c(n):M,i=n?h(n):f,u=r.exec(e);if(!u)return!1;for(var l=(new t).fromDate(s&&!isNaN(s.getTime())?s:new Date(1970,0,1,0)),g=0;g<u.length-1;g++)i[g]&&i[g].call(l,u[g+1]);var m=l.toDate();return parseInt(l.day,10)!==m.getDate()?!1:m},p.getDateForAttribute=function(t,e){var n;if("today"===e){var r=new Date;n=new Date(r.getFullYear(),r.getMonth(),r.getDate()+("maxDate"===t?1:0),0,0,0,"minDate"===t?0:-1)}else n=angular.isString(e)&&e.match(/^".+"$/)?new Date(e.substr(1,e.length-2)):s(e)?new Date(parseInt(e,10)):angular.isString(e)&&0===e.length?"minDate"===t?-1/0:+1/0:new Date(e);return n},p.getTimeForAttribute=function(t,e){var n;return n="now"===e?(new Date).setFullYear(1970,0,1):angular.isString(e)&&e.match(/^".+"$/)?new Date(e.substr(1,e.length-2)).setFullYear(1970,0,1):s(e)?new Date(parseInt(e,10)).setFullYear(1970,0,1):angular.isString(e)&&0===e.length?"minTime"===t?-1/0:+1/0:p.parse(e,new Date(1970,0,1,0))},p.daylightSavingAdjust=function(t){return t?(t.setHours(t.getHours()>12?t.getHours()+2:0),t):null},p.init(),p};return u}]}]);
//# sourceMappingURL=date-parser.min.js.map