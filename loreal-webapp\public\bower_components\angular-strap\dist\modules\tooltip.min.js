/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.tooltip",["mgcrea.ngStrap.helpers.dimensions"]).provider("$tooltip",function(){var t=this.defaults={animation:"am-fade",customClass:"",prefixClass:"tooltip",prefixEvent:"tooltip",container:!1,target:!1,placement:"top",template:"tooltip/tooltip.tpl.html",contentTemplate:!1,trigger:"hover focus",keyboard:!1,html:!1,show:!1,title:"",type:"",delay:0,autoClose:!1,bsEnabled:!0};this.$get=["$window","$rootScope","$compile","$q","$templateCache","$http","$animate","$sce","dimensions","$$rAF","$timeout",function(e,n,o,i,a,l,r,s,u,c,f){function p(e,i){function a(){K.$emit(O.prefixEvent+".show",F)}function l(){if(K.$emit(O.prefixEvent+".hide",F),q===W){if(R&&"focus"===O.trigger)return e[0].blur();P()}}function p(){var t=O.trigger.split(" ");angular.forEach(t,function(t){"click"===t?e.on("click",F.toggle):"manual"!==t&&(e.on("hover"===t?"mouseenter":"focus",F.enter),e.on("hover"===t?"mouseleave":"blur",F.leave),"button"===A&&"hover"!==t&&e.on($?"touchstart":"mousedown",F.$onFocusElementMouseDown))})}function v(){for(var t=O.trigger.split(" "),n=t.length;n--;){var o=t[n];"click"===o?e.off("click",F.toggle):"manual"!==o&&(e.off("hover"===o?"mouseenter":"focus",F.enter),e.off("hover"===o?"mouseleave":"blur",F.leave),"button"===A&&"hover"!==o&&e.off($?"touchstart":"mousedown",F.$onFocusElementMouseDown))}}function y(){"focus"!==O.trigger?q.on("keyup",F.$onKeyUp):e.on("keyup",F.$onFocusKeyUp)}function k(){"focus"!==O.trigger?q.off("keyup",F.$onKeyUp):e.off("keyup",F.$onFocusKeyUp)}function S(){f(function(){q.on("click",E),w.on("click",F.hide),z=!0},0,!1)}function C(){z&&(q.off("click",E),w.off("click",F.hide),z=!1)}function E(t){t.stopPropagation()}function x(t){t=t||O.target||e;var n=t[0],o=n.getBoundingClientRect();null===o.width&&(o=angular.extend({},o,{width:o.right-o.left,height:o.bottom-o.top}));var i;return i="body"===O.container?u.offset(n):u.position(n),angular.extend({},o,i)}function D(t,e,n,o){var i,a=t.split("-");switch(a[0]){case"right":i={top:e.top+e.height/2-o/2,left:e.left+e.width};break;case"bottom":i={top:e.top+e.height,left:e.left+e.width/2-n/2};break;case"left":i={top:e.top+e.height/2-o/2,left:e.left-n};break;default:i={top:e.top-o,left:e.left+e.width/2-n/2}}if(!a[1])return i;if("top"===a[0]||"bottom"===a[0])switch(a[1]){case"left":i.left=e.left;break;case"right":i.left=e.left+e.width-n}else if("left"===a[0]||"right"===a[0])switch(a[1]){case"top":i.top=e.top-o;break;case"bottom":i.top=e.top+e.height}return i}function T(t,e){q.css({top:t+"px",left:e+"px"})}function P(){clearTimeout(H),F.$isShown&&null!==q&&(O.autoClose&&C(),O.keyboard&&k()),B&&(B.$destroy(),B=null),q&&(q.remove(),q=F.$element=null)}var F={},A=e[0].nodeName.toLowerCase(),O=F.$options=angular.extend({},t,i);F.$promise=g(O.template);var K=F.$scope=O.scope&&O.scope.$new()||n.$new();if(O.delay&&angular.isString(O.delay)){var U=O.delay.split(",").map(parseFloat);O.delay=U.length>1?{show:U[0],hide:U[1]}:U[0]}F.$id=O.id||e.attr("id")||"",O.title&&(K.title=s.trustAsHtml(O.title)),K.$setEnabled=function(t){K.$$postDigest(function(){F.setEnabled(t)})},K.$hide=function(){K.$$postDigest(function(){F.hide()})},K.$show=function(){K.$$postDigest(function(){F.show()})},K.$toggle=function(){K.$$postDigest(function(){F.toggle()})},F.$isShown=K.$isShown=!1;var H,M;O.contentTemplate&&(F.$promise=F.$promise.then(function(t){var e=angular.element(t);return g(O.contentTemplate).then(function(t){var n=d('[ng-bind="content"]',e[0]);return n.length||(n=d('[ng-bind="title"]',e[0])),n.removeAttr("ng-bind").html(t),e[0].outerHTML})}));var j,q,L,N,B;F.$promise.then(function(t){angular.isObject(t)&&(t=t.data),O.html&&(t=t.replace(b,'ng-bind-html="')),t=m.apply(t),L=t,j=o(t),F.init()}),F.init=function(){O.delay&&angular.isNumber(O.delay)&&(O.delay={show:O.delay,hide:O.delay}),"self"===O.container?N=e:angular.isElement(O.container)?N=O.container:O.container&&(N=d(O.container)),p(),O.target&&(O.target=angular.isElement(O.target)?O.target:d(O.target)),O.show&&K.$$postDigest(function(){"focus"===O.trigger?e[0].focus():F.show()})},F.destroy=function(){v(),P(),K.$destroy()},F.enter=function(){return clearTimeout(H),M="in",O.delay&&O.delay.show?void(H=setTimeout(function(){"in"===M&&F.show()},O.delay.show)):F.show()},F.show=function(){if(O.bsEnabled&&!F.$isShown){K.$emit(O.prefixEvent+".show.before",F);var t,n;O.container?(t=N,n=N[0].lastChild?angular.element(N[0].lastChild):null):(t=null,n=e),q&&P(),B=F.$scope.$new(),q=F.$element=j(B,function(){}),q.css({top:"-9999px",left:"-9999px",display:"block",visibility:"hidden"}),O.animation&&q.addClass(O.animation),O.type&&q.addClass(O.prefixClass+"-"+O.type),O.customClass&&q.addClass(O.customClass);var o=r.enter(q,t,n,a);o&&o.then&&o.then(a),F.$isShown=K.$isShown=!0,h(K),c(function(){F.$applyPlacement(),q&&q.css({visibility:"visible"})}),O.keyboard&&("focus"!==O.trigger&&F.focus(),y()),O.autoClose&&S()}},F.leave=function(){return clearTimeout(H),M="out",O.delay&&O.delay.hide?void(H=setTimeout(function(){"out"===M&&F.hide()},O.delay.hide)):F.hide()};var R,W;F.hide=function(t){if(F.$isShown){K.$emit(O.prefixEvent+".hide.before",F),R=t,W=q;var e=r.leave(q,l);e&&e.then&&e.then(l),F.$isShown=K.$isShown=!1,h(K),O.keyboard&&null!==q&&k(),O.autoClose&&null!==q&&C()}},F.toggle=function(){F.$isShown?F.leave():F.enter()},F.focus=function(){q[0].focus()},F.setEnabled=function(t){O.bsEnabled=t},F.$applyPlacement=function(){if(q){var n=O.placement,o=/\s?auto?\s?/i,i=o.test(n);i&&(n=n.replace(o,"")||t.placement),q.addClass(O.placement);var a=x(),l=q.prop("offsetWidth"),r=q.prop("offsetHeight");if(i){var s=n,u=O.container?angular.element(document.querySelector(O.container)):e.parent(),c=x(u);s.indexOf("bottom")>=0&&a.bottom+r>c.bottom?n=s.replace("bottom","top"):s.indexOf("top")>=0&&a.top-r<c.top&&(n=s.replace("top","bottom")),("right"===s||"bottom-left"===s||"top-left"===s)&&a.right+l>c.width?n="right"===s?"left":n.replace("left","right"):("left"===s||"bottom-right"===s||"top-right"===s)&&a.left-l<c.left&&(n="left"===s?"right":n.replace("right","left")),q.removeClass(s).addClass(n)}var f=D(n,a,l,r);T(f.top,f.left)}},F.$onKeyUp=function(t){27===t.which&&F.$isShown&&(F.hide(),t.stopPropagation())},F.$onFocusKeyUp=function(t){27===t.which&&(e[0].blur(),t.stopPropagation())},F.$onFocusElementMouseDown=function(t){t.preventDefault(),t.stopPropagation(),F.$isShown?e[0].blur():e[0].focus()};var z=!1;return F}function h(t){t.$$phase||t.$root&&t.$root.$$phase||t.$digest()}function d(t,e){return angular.element((e||document).querySelectorAll(t))}function g(t){return v[t]?v[t]:v[t]=i.when(a.get(t)||l.get(t)).then(function(e){return angular.isObject(e)?(a.put(t,e.data),e.data):e})}var m=String.prototype.trim,$="createTouch"in e.document,b=/ng-bind="/gi,w=angular.element(e.document),v={};return p}]}).directive("bsTooltip",["$window","$location","$sce","$tooltip","$$rAF",function(t,e,n,o,i){return{restrict:"EAC",scope:!0,link:function(t,e,a){var l={scope:t};angular.forEach(["template","contentTemplate","placement","container","target","delay","trigger","keyboard","html","animation","backdropAnimation","type","customClass","id"],function(t){angular.isDefined(a[t])&&(l[t]=a[t])}),t.hasOwnProperty("title")||(t.title=""),a.$observe("title",function(e){if(angular.isDefined(e)||!t.hasOwnProperty("title")){var o=t.title;t.title=n.trustAsHtml(e),angular.isDefined(o)&&i(function(){r&&r.$applyPlacement()})}}),a.bsTooltip&&t.$watch(a.bsTooltip,function(e,n){angular.isObject(e)?angular.extend(t,e):t.title=e,angular.isDefined(n)&&i(function(){r&&r.$applyPlacement()})},!0),a.bsShow&&t.$watch(a.bsShow,function(t){r&&angular.isDefined(t)&&(angular.isString(t)&&(t=!!t.match(/true|,?(tooltip),?/i)),t===!0?r.show():r.hide())}),a.bsEnabled&&t.$watch(a.bsEnabled,function(t){r&&angular.isDefined(t)&&(angular.isString(t)&&(t=!!t.match(/true|1|,?(tooltip),?/i)),r.setEnabled(t===!1?!1:!0))});var r=o(e,l);t.$on("$destroy",function(){r&&r.destroy(),l=null,r=null})}}}]);
//# sourceMappingURL=tooltip.min.js.map