{"name": "ui-select", "version": "0.8.4", "homepage": "https://github.com/angular-ui/ui-select", "authors": ["AngularUI"], "description": "AngularJS ui-select", "main": ["dist/select.js", "dist/select.css"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "src", "test", "gulpfile.js", "karma.conf.js", "examples"], "dependencies": {"angular": ">=1.2.18"}, "devDependencies": {"jquery": "~1.11", "angular-sanitize": ">=1.2.18", "angular-mocks": ">=1.2.18"}, "_release": "0.8.4", "_resolution": {"type": "version", "tag": "v0.8.4", "commit": "440e10023c9b7b6b2605f4415e33c1d7bd7ed831"}, "_source": "git://github.com/angular-ui/ui-select.git", "_target": "~0.8.3", "_originalSource": "angular-ui-select"}