{"version": 3, "sources": ["datepicker/datepicker.js"], "names": [], "mappings": "qBASQ,OAAA,4IAKF,cAAS,cAET,GAAU,KAAA,UACV,UAAM,UACN,YAAO,qCAEP,SAAA,iCACA,QAAA,QACA,WAAA,EACA,UAAA,EACA,MAAA,EACA,MAAA,EAEA,WAAA,EACA,SAAA,OACA,WAAA,YACA,gBAAW,KACX,UAAU,KACV,YAAU,MACV,WAAW,OACX,iBAAS,YACT,gBAAW,OACX,cAAA,EACA,WAAU,EACV,SAAA,6BAGF,QAAK,cAEH,mBAAa,GACb,SAAI,mCACJ,UAAI,0CAGJ,MAAS,UAAA,YAAkB,aAAqB,OAAQ,iBAAA,kBAAA,WAAA,WAAA,SAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,oEAsJtD,QAAA,KACE,EAAG,GAAA,0CA7ID,EAAc,EAAA,MAClB,EAAY,EAAS,SACjB,EAAA,EAAW,MACf,GAAM,YAAgB,EAAA,WAAA,EAAA,qDAOtB,GAAM,MAAA,EAAU,YACd,UAAY,EAAO,iCAErB,IAAA,GAAM,EAAc,OAAS,EAAO,SAIlC,QAAY,SAAS,qFAOrB,EAAW,SAAO,EAAS,MAAO,GAAK,EAAY,OAAA,WAKnD,OAAmB,SAAA,4CAGrB,EAAY,MAAA,EACV,EAAQ,OAAA,KAAA,EAAqB,kBAM/B,EAAY,oBAAkB,SAAY,yBAExC,KAAI,GAAA,GAAQ,EAAA,EAAO,EAAA,KAAW,OAAA,EAAA,EAAa,IAC3C,QAAU,QAAA,EAAS,KAAM,GAAA,EAAA,mBAIrB,OAAS,SAAa,EAAA,WAEnB,OAAA,EAAA,cAAA,EAAA,WAAA,GAAA,MAAA,KACL,EAAQ,OAAO,GACf,EAAA,cAAoB,QAAM,KAAQ,IAClC,EAAA,oIAMF,EAAc,QAAA,EAAA,MAAA,GACd,EAAU,iCAMZ,EAAA,MAAY,sBAEV,EAAG,UAKL,EAAY,OAAA,SAAkB,GAE1B,KAAQ,GAAc,EAAK,6CAKtB,gBAAmB,uGAO5B,MAAA,GAAY,WAAc,oEAOxB,YAAY,SAAA,iBAKR,EAAA,GAAA,MAAA,KAAA,IAAA,EAAA,MAAA,EAAA,MAAA,GAAA,EAAA,EAAA,OAAA,EAAA,OAAA,GAAA,EAAA,GACJ,SAAI,OAAA,GAAA,KAAA,EAAA,iBAAA,MAAA,EAAA,cAAA,KAAA,EAAA,6BAIC,aAAY,SAAS,2BAGxB,oDAImC,YAAvC,EAAY,GAAA,SAAa,gBACnB,EAAC,EAAmB,UAExB,EAAI,eAAA,aAIA,WAAmB,SAAK,yBACnB,KAAA,EAAA,WAAA,EAAA,WAAA,EAAA,aACL,8GAKI,EAAU,MAAA,EAOlB,GAAG,UAAW,oBAgBZ,GAAQ,EAAK,OACL,KAAK,iBACb,IAAW,EAAS,qCAEtB,GAAA,IAAA,qBAAA,mBAGE,EAAA,KAAW,OAAA,QACf,EAAY,KAAA,WAAU,QACpB,EAAG,GAAA,QAAY,8BAMjB,GAAY,QAAA,WACZ,GAAY,EAAO,WACjB,EAAA,IAAA,QAAA,WAKE,GAAI,EAAY,OAChB,KAAY,iBAIR,wBAGJ,EAAQ,SAAY,GAAA,EAAA,aAAA,YAAA,EAAA,cACxB,EAAY,UACN,EAAA,GAAA,UAAsB,EAAA,cAEvB,GAAA,OAGH,GAAM,EAAA,4CAGR,EAAO,SAAA,IAAA,EAAA,aAAA,YAAA,EAAA,wDAIT,EAAA,UArME,IADI,QAAc,QAAS,EAAA,SAAiB,MACxC,8BAAqB,KAAA,EAAA,UAAA,YACzB,EAAI,eAAsB,GAAA,UAAA,QAC1B,GAAI,OAAQ,EAAY,KAAA,EAAA,oBA0M7B,EAAU,SAAA,kBAOP,gBAAS,UAAA,SAAA,KAAA,iBAAA,cAAA,cAAA,SAAA,EAAA,EAAA,EAAA,EAAA,EAAA,gGAMF,uDAkDD,GAAiB,SACjB,IAAA,EAAgB,SAAoB,aAetC,GAA+B,yBAE/B,GAAG,GAAS,MAAW,EAAA,SAAa,UAAA,EAAA,WAAA,EAAA,SAAA,qGAItC,EAAW,aAAS,MAAQ,2BAGvB,IAAC,EAAW,WAAA,YAkEf,KACA,OAAA,EAAa,YAAA,MAAA,EAAA,WAAA,WAAA,GAAA,EAAA,EAAA,WAAA,EAAA,eAzIb,IAAI,MAAe,EAAQ,WAAU,WAClC,SAAQ,YAAS,YAAW,QAAa,UAAe,WAAA,OAAA,YAAA,WAAA,YAAA,WAAA,aAAA,kBAAA,YAAA,eAAA,YAAA,YAAA,YAAA,OAAA,YAAA,UAAA,WAAA,YAAA,qBAAA,MAAA,SAAA,GAC3D,QAAA,UAAoB,EAAA,MAAW,EAAS,GAAA,EAAW,MAIrD,EAAI,QAAA,EAAa,OAAA,EAAY,OAAS,SAAY,GAClD,GAAqB,QAAA,UAAA,kEAErB,KAAe,EAAA,EAAQ,OAAmB,EAAA,SAI1C,IAAI,GAAa,EAAS,EAAM,EAAQ,KAC/B,EAAA,kEAMT,EAAiB,SAAA,EAAW,+BAIxB,EAAW,GAAS,OAAO,EAAW,WAAA,KAAoB,EAAK,OAAA,EAAA,uBAG/D,SAAA,UAAA,WAA0B,SAAW,+FAMvC,MAAA,EAAkB,SAAW,KAAA,EAAA,QAAA,GAC5B,EAAA,EAAA,kBAKG,OAAC,EAAW,QAAO,WACvB,EAAO,OAAA,EAAA,0GAeF,GACD,EAAA,oBAA8B,oCAqBlC,IAAI,EAKF,MAJF,GAAI,aAAoB,QAAA,GAItB,QAEA,GAAA,EAA0B,MAAA,EAAA,EAAA,6CAE5B,GAAW,aAAa,QAAU,IAKhC,EAAkB,GAEA,WAAlB,EAAO,SACF,EAAA,EAAA,EAAA,iBAAA,EAAA,YACsB,WAApB,EAAS,kGAKT,EAAA,WAAiB,cAEtB,GAAA,MAAA,EAAA,iBAKG,YAAW,KAAA,SAAa,kBAG7B,QAAO,YAAS,IAAa,OAAA,EACxB,IACE,QAAS,OAAA,gGAOP,GAOX,EAAQ,WAAI,+JAoClB,aAAA,EAAS,OAAU,GACjB,EAAS,KAAI,EAAK,OAAK,EAAA,oCAOjB,EAAA,EAAQ,GAAO,EAhBR,KAAA,UACb,UAAU,cACD,QAiBP,MAAI,iBAAe,cAAA,OAAA,SAAA,EAAA,EAAA,SAEjB,UAAO,GAET,GAAI,GAAA,EAAa,oBAGb,EAAA,EAAA,KACA,EAAA,SAAqB,EAAK,+BAG1B,EAAY,GAAM,OAAU,EAAA,WAAsB,KAAU,EAAA,OAAY,EAAM,oCAG9E,EAAS,EAAA,MAAA,EAAA,WAAA,OAAA,EAAA,MAAA,EAAA,EAAA,YACT,EAAgB,EAAA,YAAA,+BAAA,EAAA,KAAA,qCAAA,SAEhB,EAAS,EAAO,QAAA,EAAA,UAAA,EAAA,oBAAA,YAAA,EAAA,WAAA,GAAA,OAChB,GAAQ,KAAS,EAAM,cAAO,MAAA,EAAA,WAAA,KAAA,EAAA,cACI,IAAvB,EAAS,+BAGR,gBACR,SACA,MAAO,0FAGX,QAAO,OAAW,GAAA,KAAA,EAAA,MAAA,cAAA,MAAA,EAAA,MAAA,WAAA,KAAA,EAAA,MAAA,YAChB,EAAI,UACA,EAAA,YAAsB,EAAA,OAC1B,EAAI,KAAY,EAAO,MAAA,sCAInB,cACF,GAAiB,GAAA,MAAA,EAAqB,KAAI,EAAK,MAAU,GAAA,EAAyB,EAAsB,oBACxG,EAAW,GAAM,OAAK,EAAmE,MAAtD,EAAA,EAA0B,SAAO,EAAW,UAAU,IAAS,EAAiB,EAAc,+CAGnI,KAAmB,IAAA,EAAA,GAAA,OAAA,EAAA,KAAA,EAAA,IAEnB,KAAA,GADe,GAAf,KACM,EAAA,EAAa,GAAN,EAAM,IACnB,EAAK,EAAQ,qBAAA,GAAA,MAAA,EAAA,cAAA,EAAA,WAAA,EAAA,UAAA,sKAGb,GAAA,MAAc,EAAS,EAAK,EAAkB,kCAEhD,EAAA,OAAY,EACV,EAAI,KAAO,EAAK,EAAA,KAAA,iCAGZ,SAAe,2IAGf,SAAQ,wBAIV,EAAS,EAAO,SAAY,EAAA,EAAA,QAAmB,OAAQ,oFAOzD,IAAO,GAAA,GAAA,EAAA,EAAA,EAAA,mBAAA,OAAA,wEAET,OAAW,CAKT,QAAI,aAEG,SAAA,MACF,EAAO,UAIR,sBAEL,MAAA,EAAA,QAAA,EAAA,GAAA,MAAA,EAAA,OACK,KAAA,EAAA,QAAA,EAAA,GAAA,MAAA,EAAA,QACU,KAAR,EAAA,QAAQ,EAAA,GAAA,MAAA,EAAA,OACT,KAAA,EAAA,UAAA,EAAA,GAAA,MAAA,EAAA,SAEP,KAAQ,WAAe,IAAO,EAAA,OAAA,GAAA,YAG1B,iBACQ,kBACR,SACA,KAAO,2DAKL,EAAS,aAAI,EAAA,QACjB,QAAS,OAAO,GAAQ,MAAK,EAAA,MAAA,WAAA,KAAA,EAAA,MAAA,YAC3B,EAAA,oBAJJ,QAAO,OAAW,GAAA,KAAA,EAAA,MAAA,cAAA,MAAA,EAAA,MAAA,WAAA,KAAA,EAAA,MAAA,YAChB,EAAI,iBAMJ,WAGA,IAAK,GADQ,GAAb,GADM,GAAa,MAAA,EAAA,KAAA,EAAA,OAEd,EAAQ,EAAA,GAAA,EAAA,2BAEf,EAAA,MAAY,KAAS,EAAM,MAAA,EAAA,EAAA,KAAA,QAAA,SAAA,EAAA,YAAA,GAAA,SAAA,KAAA,WAAA,oCAG3B,EAAA,YAAqB,EACnB,EAAI,KAAA,EAAY,EAAS,KAAK,OAC9B,KAAA,OAAO,GAET,WAAW,SAAS,GAClB,MAAK,GAAO,OAAO,EAAA,gBAAA,EAAA,MAAA,eAAA,EAAA,aAAA,EAAA,MAAA,mCAGnB,GAAI,IAAA,GAAc,MAAO,EAAA,cAAM,EAAA,WAAA,EAAA,EAC/B,OAAI,GAAc,EAAK,SAAO,EAAA,UAAA,EAAA,mBAEvB,SAAA,MACF,EAAO,gCAIR,EAAM,GAAA,MAAW,EAAA,MAEtB,MAAA,EAAA,QAAA,EAAA,SAAA,EAAA,GACK,KAAA,EAAA,QAAA,EAAA,SAAA,EAAA,GACU,KAAR,EAAA,QAAQ,EAAA,SAAA,EAAA,GACT,KAAA,EAAA,SAAA,EAAA,SAAA,EAAA,GAEP,KAAQ,WAAe,IAAO,EAAA,OAAA,GAAA,YAG1B,gBACQ,iBACR,SACA,KAAO,mGAGX,QAAO,OAAW,GAAA,KAAA,EAAA,MAAA,cAAA,MAAA,EAAA,MAAA,WAAA,KAAA,EAAA,MAAA,YAChB,EAAI,UACA,EAAQ,gBAAI,EAAA,OAChB,QAAS,OAAO,GAAQ,KAAK,EAAA,MAAA,cAAA,MAAA,EAAA,MAAA,WAAA,KAAA,EAAA,MAAA,YAC3B,EAAO,0BAGT,WAGA,IAAK,GADQ,GADb,EAAM,EAAa,KAAA,EAAA,MAAA,EAAA,KAAA,OACnB,KACK,EAAQ,EAAA,GAAA,EAAA,wBAEf,EAAA,MAAY,KAAS,EAAM,MAAA,EAAA,EAAA,KAAA,QAAA,SAAA,EAAA,YAAA,GAAA,SAAA,KAAA,WAAA,gDAG3B,EAAA,YAAqB,EACnB,EAAI,KAAA,EAAY,EAAS,KAAK,OAC9B,KAAA,OAAO,GAET,WAAW,SAAS,GAClB,MAAK,GAAO,OAAO,EAAA,gBAAA,EAAA,MAAA,sCAGnB,GAAI,IAAA,GAAa,MAAO,EAAM,cAAA,EAAA,EAAA,SAC1B,GAAc,EAAK,SAAO,EAAA,UAAA,EAAA,mBAEvB,SAAA,MACF,EAAO,mCAIR,EAAM,GAAA,MAAW,EAAA,mEAIpB,KAAA,EAAA,QAAA,EAAA,QAAA,EAAA,GACoB,KAAlB,EAAQ,SAAgB,EAAA,QAAgB,EAAY,4GAQhE,SAAA", "file": "datepicker.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.datepicker', [\n  'mgcrea.ngStrap.helpers.dateParser',\n  'mgcrea.ngStrap.helpers.dateFormatter',\n  'mgcrea.ngStrap.tooltip'])\n\n  .provider('$datepicker', function() {\n\n    var defaults = this.defaults = {\n      animation: 'am-fade',\n      prefixClass: 'datepicker',\n      placement: 'bottom-left',\n      template: 'datepicker/datepicker.tpl.html',\n      trigger: 'focus',\n      container: false,\n      keyboard: true,\n      html: false,\n      delay: 0,\n      // lang: $locale.id,\n      useNative: false,\n      dateType: 'date',\n      dateFormat: 'shortDate',\n      modelDateFormat: null,\n      dayFormat: 'dd',\n      monthFormat: 'MMM',\n      yearFormat: 'yyyy',\n      monthTitleFormat: 'MMMM yyyy',\n      yearTitleFormat: 'yyyy',\n      strictFormat: false,\n      autoclose: false,\n      minDate: -Infinity,\n      maxDate: +Infinity,\n      startView: 0,\n      minView: 0,\n      startWeek: 0,\n      daysOfWeekDisabled: '',\n      iconLeft: 'glyphicon glyphicon-chevron-left',\n      iconRight: 'glyphicon glyphicon-chevron-right'\n    };\n\n    this.$get = function($window, $document, $rootScope, $sce, $dateFormatter, datepickerViews, $tooltip, $timeout) {\n\n      var bodyEl = angular.element($window.document.body);\n      var isNative = /(ip(a|o)d|iphone|android)/ig.test($window.navigator.userAgent);\n      var isTouch = ('createTouch' in $window.document) && isNative;\n      if(!defaults.lang) defaults.lang = $dateFormatter.getDefaultLocale();\n\n      function DatepickerFactory(element, controller, config) {\n\n        var $datepicker = $tooltip(element, angular.extend({}, defaults, config));\n        var parentScope = config.scope;\n        var options = $datepicker.$options;\n        var scope = $datepicker.$scope;\n        if(options.startView) options.startView -= options.minView;\n\n        // View vars\n\n        var pickerViews = datepickerViews($datepicker);\n        $datepicker.$views = pickerViews.views;\n        var viewDate = pickerViews.viewDate;\n        scope.$mode = options.startView;\n        scope.$iconLeft = options.iconLeft;\n        scope.$iconRight = options.iconRight;\n        var $picker = $datepicker.$views[scope.$mode];\n\n        // Scope methods\n\n        scope.$select = function(date) {\n          $datepicker.select(date);\n        };\n        scope.$selectPane = function(value) {\n          $datepicker.$selectPane(value);\n        };\n        scope.$toggleMode = function() {\n          $datepicker.setMode((scope.$mode + 1) % $datepicker.$views.length);\n        };\n\n        // Public methods\n\n        $datepicker.update = function(date) {\n          // console.warn('$datepicker.update() newValue=%o', date);\n          if(angular.isDate(date) && !isNaN(date.getTime())) {\n            $datepicker.$date = date;\n            $picker.update.call($picker, date);\n          }\n          // Build only if pristine\n          $datepicker.$build(true);\n        };\n\n        $datepicker.updateDisabledDates = function(dateRanges) {\n          options.disabledDateRanges = dateRanges;\n          for(var i = 0, l = scope.rows.length; i < l; i++) {\n            angular.forEach(scope.rows[i], $datepicker.$setDisabledEl);\n          }\n        };\n\n        $datepicker.select = function(date, keep) {\n          // console.warn('$datepicker.select', date, scope.$mode);\n          if(!angular.isDate(controller.$dateValue)) controller.$dateValue = new Date(date);\n          if(!scope.$mode || keep) {\n            controller.$setViewValue(angular.copy(date));\n            controller.$render();\n            if(options.autoclose && !keep) {\n              $timeout(function() { $datepicker.hide(true); });\n            }\n          } else {\n            angular.extend(viewDate, {year: date.getFullYear(), month: date.getMonth(), date: date.getDate()});\n            $datepicker.setMode(scope.$mode - 1);\n            $datepicker.$build();\n          }\n        };\n\n        $datepicker.setMode = function(mode) {\n          // console.warn('$datepicker.setMode', mode);\n          scope.$mode = mode;\n          $picker = $datepicker.$views[scope.$mode];\n          $datepicker.$build();\n        };\n\n        // Protected methods\n\n        $datepicker.$build = function(pristine) {\n          // console.warn('$datepicker.$build() viewDate=%o', viewDate);\n          if(pristine === true && $picker.built) return;\n          if(pristine === false && !$picker.built) return;\n          $picker.build.call($picker);\n        };\n\n        $datepicker.$updateSelected = function() {\n          for(var i = 0, l = scope.rows.length; i < l; i++) {\n            angular.forEach(scope.rows[i], updateSelected);\n          }\n        };\n\n        $datepicker.$isSelected = function(date) {\n          return $picker.isSelected(date);\n        };\n\n        $datepicker.$setDisabledEl = function(el) {\n          el.disabled = $picker.isDisabled(el.date);\n        };\n\n        $datepicker.$selectPane = function(value) {\n          var steps = $picker.steps;\n          // set targetDate to first day of month to avoid problems with\n          // date values rollover. This assumes the viewDate does not\n          // depend on the day of the month\n          var targetDate = new Date(Date.UTC(viewDate.year + ((steps.year || 0) * value), viewDate.month + ((steps.month || 0) * value), 1));\n          angular.extend(viewDate, {year: targetDate.getUTCFullYear(), month: targetDate.getUTCMonth(), date: targetDate.getUTCDate()});\n          $datepicker.$build();\n        };\n\n        $datepicker.$onMouseDown = function(evt) {\n          // Prevent blur on mousedown on .dropdown-menu\n          evt.preventDefault();\n          evt.stopPropagation();\n          // Emulate click for mobile devices\n          if(isTouch) {\n            var targetEl = angular.element(evt.target);\n            if(targetEl[0].nodeName.toLowerCase() !== 'button') {\n              targetEl = targetEl.parent();\n            }\n            targetEl.triggerHandler('click');\n          }\n        };\n\n        $datepicker.$onKeyDown = function(evt) {\n          if (!/(38|37|39|40|13)/.test(evt.keyCode) || evt.shiftKey || evt.altKey) return;\n          evt.preventDefault();\n          evt.stopPropagation();\n\n          if(evt.keyCode === 13) {\n            if(!scope.$mode) {\n              return $datepicker.hide(true);\n            } else {\n              return scope.$apply(function() { $datepicker.setMode(scope.$mode - 1); });\n            }\n          }\n\n          // Navigate with keyboard\n          $picker.onKeyDown(evt);\n          parentScope.$digest();\n        };\n\n        // Private\n\n        function updateSelected(el) {\n          el.selected = $datepicker.$isSelected(el.date);\n        }\n\n        function focusElement() {\n          element[0].focus();\n        }\n\n        // Overrides\n\n        var _init = $datepicker.init;\n        $datepicker.init = function() {\n          if(isNative && options.useNative) {\n            element.prop('type', 'date');\n            element.css('-webkit-appearance', 'textfield');\n            return;\n          } else if(isTouch) {\n            element.prop('type', 'text');\n            element.attr('readonly', 'true');\n            element.on('click', focusElement);\n          }\n          _init();\n        };\n\n        var _destroy = $datepicker.destroy;\n        $datepicker.destroy = function() {\n          if(isNative && options.useNative) {\n            element.off('click', focusElement);\n          }\n          _destroy();\n        };\n\n        var _show = $datepicker.show;\n        $datepicker.show = function() {\n          _show();\n          // use timeout to hookup the events to prevent\n          // event bubbling from being processed imediately.\n          $timeout(function() {\n            // if $datepicker is no longer showing, don't setup events\n            if(!$datepicker.$isShown) return;\n            $datepicker.$element.on(isTouch ? 'touchstart' : 'mousedown', $datepicker.$onMouseDown);\n            if(options.keyboard) {\n              element.on('keydown', $datepicker.$onKeyDown);\n            }\n          }, 0, false);\n        };\n\n        var _hide = $datepicker.hide;\n        $datepicker.hide = function(blur) {\n          if(!$datepicker.$isShown) return;\n          $datepicker.$element.off(isTouch ? 'touchstart' : 'mousedown', $datepicker.$onMouseDown);\n          if(options.keyboard) {\n            element.off('keydown', $datepicker.$onKeyDown);\n          }\n          _hide(blur);\n        };\n\n        return $datepicker;\n\n      }\n\n      DatepickerFactory.defaults = defaults;\n      return DatepickerFactory;\n\n    };\n\n  })\n\n  .directive('bsDatepicker', function($window, $parse, $q, $dateFormatter, $dateParser, $datepicker) {\n\n    var defaults = $datepicker.defaults;\n    var isNative = /(ip(a|o)d|iphone|android)/ig.test($window.navigator.userAgent);\n\n    return {\n      restrict: 'EAC',\n      require: 'ngModel',\n      link: function postLink(scope, element, attr, controller) {\n\n        // Directive options\n        var options = {scope: scope, controller: controller};\n        angular.forEach(['placement', 'container', 'delay', 'trigger', 'keyboard', 'html', 'animation', 'template', 'autoclose', 'dateType', 'dateFormat', 'modelDateFormat', 'dayFormat', 'strictFormat', 'startWeek', 'startDate', 'useNative', 'lang', 'startView', 'minView', 'iconLeft', 'iconRight', 'daysOfWeekDisabled', 'id'], function(key) {\n          if(angular.isDefined(attr[key])) options[key] = attr[key];\n        });\n\n        // Visibility binding support\n        attr.bsShow && scope.$watch(attr.bsShow, function(newValue, oldValue) {\n          if(!datepicker || !angular.isDefined(newValue)) return;\n          if(angular.isString(newValue)) newValue = !!newValue.match(/true|,?(datepicker),?/i);\n          newValue === true ? datepicker.show() : datepicker.hide();\n        });\n\n        // Initialize datepicker\n        var datepicker = $datepicker(element, controller, options);\n        options = datepicker.$options;\n        // Set expected iOS format\n        if(isNative && options.useNative) options.dateFormat = 'yyyy-MM-dd';\n\n        var lang = options.lang;\n\n        var formatDate = function(date, format) {\n          return $dateFormatter.formatDate(date, format, lang);\n        };\n\n        var dateParser = $dateParser({format: options.dateFormat, lang: lang, strict: options.strictFormat});\n\n        // Observe attributes for changes\n        angular.forEach(['minDate', 'maxDate'], function(key) {\n          // console.warn('attr.$observe(%s)', key, attr[key]);\n          angular.isDefined(attr[key]) && attr.$observe(key, function(newValue) {\n            // console.warn('attr.$observe(%s)=%o', key, newValue);\n            datepicker.$options[key] = dateParser.getDateForAttribute(key, newValue);\n            // Build only if dirty\n            !isNaN(datepicker.$options[key]) && datepicker.$build(false);\n            validateAgainstMinMaxDate(controller.$dateValue);\n          });\n        });\n\n        // Watch model for changes\n        scope.$watch(attr.ngModel, function(newValue, oldValue) {\n          datepicker.update(controller.$dateValue);\n        }, true);\n\n        // Normalize undefined/null/empty array,\n        // so that we don't treat changing from undefined->null as a change.\n        function normalizeDateRanges(ranges) {\n          if (!ranges || !ranges.length) return null;\n          return ranges;\n        }\n\n        if (angular.isDefined(attr.disabledDates)) {\n          scope.$watch(attr.disabledDates, function(disabledRanges, previousValue) {\n            disabledRanges = normalizeDateRanges(disabledRanges);\n            previousValue = normalizeDateRanges(previousValue);\n\n            if (disabledRanges) {\n              datepicker.updateDisabledDates(disabledRanges);\n            }\n          });\n        }\n\n        function validateAgainstMinMaxDate(parsedDate) {\n          if (!angular.isDate(parsedDate)) return;\n          var isMinValid = isNaN(datepicker.$options.minDate) || parsedDate.getTime() >= datepicker.$options.minDate;\n          var isMaxValid = isNaN(datepicker.$options.maxDate) || parsedDate.getTime() <= datepicker.$options.maxDate;\n          var isValid = isMinValid && isMaxValid;\n          controller.$setValidity('date', isValid);\n          controller.$setValidity('min', isMinValid);\n          controller.$setValidity('max', isMaxValid);\n          // Only update the model when we have a valid date\n          if(isValid) controller.$dateValue = parsedDate;\n        }\n\n        // viewValue -> $parsers -> modelValue\n        controller.$parsers.unshift(function(viewValue) {\n          // console.warn('$parser(\"%s\"): viewValue=%o', element.attr('ng-model'), viewValue);\n          // Null values should correctly reset the model value & validity\n          if(!viewValue) {\n            controller.$setValidity('date', true);\n            // BREAKING CHANGE:\n            // return null (not undefined) when input value is empty, so angularjs 1.3\n            // ngModelController can go ahead and run validators, like ngRequired\n            return null;\n          }\n          var parsedDate = dateParser.parse(viewValue, controller.$dateValue);\n          if(!parsedDate || isNaN(parsedDate.getTime())) {\n            controller.$setValidity('date', false);\n            // return undefined, causes ngModelController to\n            // invalidate model value\n            return;\n          } else {\n            validateAgainstMinMaxDate(parsedDate);\n          }\n          if(options.dateType === 'string') {\n            return formatDate(parsedDate, options.modelDateFormat || options.dateFormat);\n          } else if(options.dateType === 'number') {\n            return controller.$dateValue.getTime();\n          } else if(options.dateType === 'unix') {\n            return controller.$dateValue.getTime() / 1000;\n          } else if(options.dateType === 'iso') {\n            return controller.$dateValue.toISOString();\n          } else {\n            return new Date(controller.$dateValue);\n          }\n        });\n\n        // modelValue -> $formatters -> viewValue\n        controller.$formatters.push(function(modelValue) {\n          // console.warn('$formatter(\"%s\"): modelValue=%o (%o)', element.attr('ng-model'), modelValue, typeof modelValue);\n          var date;\n          if(angular.isUndefined(modelValue) || modelValue === null) {\n            date = NaN;\n          } else if(angular.isDate(modelValue)) {\n            date = modelValue;\n          } else if(options.dateType === 'string') {\n            date = dateParser.parse(modelValue, null, options.modelDateFormat);\n          } else if(options.dateType === 'unix') {\n            date = new Date(modelValue * 1000);\n          } else {\n            date = new Date(modelValue);\n          }\n          // Setup default value?\n          // if(isNaN(date.getTime())) {\n          //   var today = new Date();\n          //   date = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0, 0);\n          // }\n          controller.$dateValue = date;\n          return getDateFormattedString();\n        });\n\n        // viewValue -> element\n        controller.$render = function() {\n          // console.warn('$render(\"%s\"): viewValue=%o', element.attr('ng-model'), controller.$viewValue);\n          element.val(getDateFormattedString());\n        };\n\n        function getDateFormattedString() {\n          return !controller.$dateValue || isNaN(controller.$dateValue.getTime()) ? '' : formatDate(controller.$dateValue, options.dateFormat);\n        }\n\n        // Garbage collection\n        scope.$on('$destroy', function() {\n          if(datepicker) datepicker.destroy();\n          options = null;\n          datepicker = null;\n        });\n\n      }\n    };\n\n  })\n\n  .provider('datepickerViews', function() {\n\n    var defaults = this.defaults = {\n      dayFormat: 'dd',\n      daySplit: 7\n    };\n\n    // Split array into smaller arrays\n    function split(arr, size) {\n      var arrays = [];\n      while(arr.length > 0) {\n        arrays.push(arr.splice(0, size));\n      }\n      return arrays;\n    }\n\n    // Modulus operator\n    function mod(n, m) {\n      return ((n % m) + m) % m;\n    }\n\n    this.$get = function($dateFormatter, $dateParser, $sce) {\n\n      return function(picker) {\n\n        var scope = picker.$scope;\n        var options = picker.$options;\n\n        var lang = options.lang;\n        var formatDate = function(date, format) {\n          return $dateFormatter.formatDate(date, format, lang);\n        };\n        var dateParser = $dateParser({format: options.dateFormat, lang: lang, strict: options.strictFormat});\n\n        var weekDaysMin = $dateFormatter.weekdaysShort(lang);\n        var weekDaysLabels = weekDaysMin.slice(options.startWeek).concat(weekDaysMin.slice(0, options.startWeek));\n        var weekDaysLabelsHtml = $sce.trustAsHtml('<th class=\"dow text-center\">' + weekDaysLabels.join('</th><th class=\"dow text-center\">') + '</th>');\n\n        var startDate = picker.$date || (options.startDate ? dateParser.getDateForAttribute('startDate', options.startDate) : new Date());\n        var viewDate = {year: startDate.getFullYear(), month: startDate.getMonth(), date: startDate.getDate()};\n        var timezoneOffset = startDate.getTimezoneOffset() * 6e4;\n\n        var views = [{\n            format: options.dayFormat,\n            split: 7,\n            steps: { month: 1 },\n            update: function(date, force) {\n              if(!this.built || force || date.getFullYear() !== viewDate.year || date.getMonth() !== viewDate.month) {\n                angular.extend(viewDate, {year: picker.$date.getFullYear(), month: picker.$date.getMonth(), date: picker.$date.getDate()});\n                picker.$build();\n              } else if(date.getDate() !== viewDate.date) {\n                viewDate.date = picker.$date.getDate();\n                picker.$updateSelected();\n              }\n            },\n            build: function() {\n              var firstDayOfMonth = new Date(viewDate.year, viewDate.month, 1), firstDayOfMonthOffset = firstDayOfMonth.getTimezoneOffset();\n              var firstDate = new Date(+firstDayOfMonth - mod(firstDayOfMonth.getDay() - options.startWeek, 7) * 864e5), firstDateOffset = firstDate.getTimezoneOffset();\n              var today = new Date().toDateString();\n              // Handle daylight time switch\n              if(firstDateOffset !== firstDayOfMonthOffset) firstDate = new Date(+firstDate + (firstDateOffset - firstDayOfMonthOffset) * 60e3);\n              var days = [], day;\n              for(var i = 0; i < 42; i++) { // < 7 * 6\n                day = dateParser.daylightSavingAdjust(new Date(firstDate.getFullYear(), firstDate.getMonth(), firstDate.getDate() + i));\n                days.push({date: day, isToday: day.toDateString() === today, label: formatDate(day, this.format), selected: picker.$date && this.isSelected(day), muted: day.getMonth() !== viewDate.month, disabled: this.isDisabled(day)});\n              }\n              scope.title = formatDate(firstDayOfMonth, options.monthTitleFormat);\n              scope.showLabels = true;\n              scope.labels = weekDaysLabelsHtml;\n              scope.rows = split(days, this.split);\n              this.built = true;\n            },\n            isSelected: function(date) {\n              return picker.$date && date.getFullYear() === picker.$date.getFullYear() && date.getMonth() === picker.$date.getMonth() && date.getDate() === picker.$date.getDate();\n            },\n            isDisabled: function(date) {\n              var time = date.getTime();\n\n              // Disabled because of min/max date.\n              if (time < options.minDate || time > options.maxDate) return true;\n\n              // Disabled due to being a disabled day of the week\n              if (options.daysOfWeekDisabled.indexOf(date.getDay()) !== -1) return true;\n\n              // Disabled because of disabled date range.\n              if (options.disabledDateRanges) {\n                for (var i = 0; i < options.disabledDateRanges.length; i++) {\n                  if (time >= options.disabledDateRanges[i].start && time <= options.disabledDateRanges[i].end) {\n                    return true;\n                  }\n                }\n              }\n\n              return false;\n            },\n            onKeyDown: function(evt) {\n              if (!picker.$date) {\n                return;\n              }\n              var actualTime = picker.$date.getTime();\n              var newDate;\n\n              if(evt.keyCode === 37) newDate = new Date(actualTime - 1 * 864e5);\n              else if(evt.keyCode === 38) newDate = new Date(actualTime - 7 * 864e5);\n              else if(evt.keyCode === 39) newDate = new Date(actualTime + 1 * 864e5);\n              else if(evt.keyCode === 40) newDate = new Date(actualTime + 7 * 864e5);\n\n              if (!this.isDisabled(newDate)) picker.select(newDate, true);\n            }\n          }, {\n            name: 'month',\n            format: options.monthFormat,\n            split: 4,\n            steps: { year: 1 },\n            update: function(date, force) {\n              if(!this.built || date.getFullYear() !== viewDate.year) {\n                angular.extend(viewDate, {year: picker.$date.getFullYear(), month: picker.$date.getMonth(), date: picker.$date.getDate()});\n                picker.$build();\n              } else if(date.getMonth() !== viewDate.month) {\n                angular.extend(viewDate, {month: picker.$date.getMonth(), date: picker.$date.getDate()});\n                picker.$updateSelected();\n              }\n            },\n            build: function() {\n              var firstMonth = new Date(viewDate.year, 0, 1);\n              var months = [], month;\n              for (var i = 0; i < 12; i++) {\n                month = new Date(viewDate.year, i, 1);\n                months.push({date: month, label: formatDate(month, this.format), selected: picker.$isSelected(month), disabled: this.isDisabled(month)});\n              }\n              scope.title = formatDate(month, options.yearTitleFormat);\n              scope.showLabels = false;\n              scope.rows = split(months, this.split);\n              this.built = true;\n            },\n            isSelected: function(date) {\n              return picker.$date && date.getFullYear() === picker.$date.getFullYear() && date.getMonth() === picker.$date.getMonth();\n            },\n            isDisabled: function(date) {\n              var lastDate = +new Date(date.getFullYear(), date.getMonth() + 1, 0);\n              return lastDate < options.minDate || date.getTime() > options.maxDate;\n            },\n            onKeyDown: function(evt) {\n              if (!picker.$date) {\n                return;\n              }\n              var actualMonth = picker.$date.getMonth();\n              var newDate = new Date(picker.$date);\n\n              if(evt.keyCode === 37) newDate.setMonth(actualMonth - 1);\n              else if(evt.keyCode === 38) newDate.setMonth(actualMonth - 4);\n              else if(evt.keyCode === 39) newDate.setMonth(actualMonth + 1);\n              else if(evt.keyCode === 40) newDate.setMonth(actualMonth + 4);\n\n              if (!this.isDisabled(newDate)) picker.select(newDate, true);\n            }\n          }, {\n            name: 'year',\n            format: options.yearFormat,\n            split: 4,\n            steps: { year: 12 },\n            update: function(date, force) {\n              if(!this.built || force || parseInt(date.getFullYear()/20, 10) !== parseInt(viewDate.year/20, 10)) {\n                angular.extend(viewDate, {year: picker.$date.getFullYear(), month: picker.$date.getMonth(), date: picker.$date.getDate()});\n                picker.$build();\n              } else if(date.getFullYear() !== viewDate.year) {\n                angular.extend(viewDate, {year: picker.$date.getFullYear(), month: picker.$date.getMonth(), date: picker.$date.getDate()});\n                picker.$updateSelected();\n              }\n            },\n            build: function() {\n              var firstYear = viewDate.year - viewDate.year % (this.split * 3);\n              var years = [], year;\n              for (var i = 0; i < 12; i++) {\n                year = new Date(firstYear + i, 0, 1);\n                years.push({date: year, label: formatDate(year, this.format), selected: picker.$isSelected(year), disabled: this.isDisabled(year)});\n              }\n              scope.title = years[0].label + '-' + years[years.length - 1].label;\n              scope.showLabels = false;\n              scope.rows = split(years, this.split);\n              this.built = true;\n            },\n            isSelected: function(date) {\n              return picker.$date && date.getFullYear() === picker.$date.getFullYear();\n            },\n            isDisabled: function(date) {\n              var lastDate = +new Date(date.getFullYear() + 1, 0, 0);\n              return lastDate < options.minDate || date.getTime() > options.maxDate;\n            },\n            onKeyDown: function(evt) {\n              if (!picker.$date) {\n                return;\n              }\n              var actualYear = picker.$date.getFullYear(),\n                  newDate = new Date(picker.$date);\n\n              if(evt.keyCode === 37) newDate.setYear(actualYear - 1);\n              else if(evt.keyCode === 38) newDate.setYear(actualYear - 4);\n              else if(evt.keyCode === 39) newDate.setYear(actualYear + 1);\n              else if(evt.keyCode === 40) newDate.setYear(actualYear + 4);\n\n              if (!this.isDisabled(newDate)) picker.select(newDate, true);\n            }\n          }];\n\n        return {\n          views: options.minView ? Array.prototype.slice.call(views, options.minView) : views,\n          viewDate: viewDate\n        };\n\n      };\n\n    };\n\n  });\n"], "sourceRoot": "/source/"}