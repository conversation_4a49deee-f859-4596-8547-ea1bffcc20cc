/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.datepicker",["mgcrea.ngStrap.helpers.dateParser","mgcrea.ngStrap.helpers.dateFormatter","mgcrea.ngStrap.tooltip"]).provider("$datepicker",function(){var e=this.defaults={animation:"am-fade",prefixClass:"datepicker",placement:"bottom-left",template:"datepicker/datepicker.tpl.html",trigger:"focus",container:!1,keyboard:!0,html:!1,delay:0,useNative:!1,dateType:"date",dateFormat:"shortDate",modelDateFormat:null,dayFormat:"dd",monthFormat:"MMM",yearFormat:"yyyy",monthTitleFormat:"MMMM yyyy",yearTitleFormat:"yyyy",strictFormat:!1,autoclose:!1,minDate:-1/0,maxDate:+1/0,startView:0,minView:0,startWeek:0,daysOfWeekDisabled:"",iconLeft:"glyphicon glyphicon-chevron-left",iconRight:"glyphicon glyphicon-chevron-right"};this.$get=["$window","$document","$rootScope","$sce","$dateFormatter","datepickerViews","$tooltip","$timeout",function(t,a,n,i,o,r,d,s){function l(t,a,n){function i(e){e.selected=l.$isSelected(e.date)}function o(){t[0].focus()}var l=d(t,angular.extend({},e,n)),g=n.scope,$=l.$options,m=l.$scope;$.startView&&($.startView-=$.minView);var f=r(l);l.$views=f.views;var h=f.viewDate;m.$mode=$.startView,m.$iconLeft=$.iconLeft,m.$iconRight=$.iconRight;var D=l.$views[m.$mode];m.$select=function(e){l.select(e)},m.$selectPane=function(e){l.$selectPane(e)},m.$toggleMode=function(){l.setMode((m.$mode+1)%l.$views.length)},l.update=function(e){angular.isDate(e)&&!isNaN(e.getTime())&&(l.$date=e,D.update.call(D,e)),l.$build(!0)},l.updateDisabledDates=function(e){$.disabledDateRanges=e;for(var t=0,a=m.rows.length;a>t;t++)angular.forEach(m.rows[t],l.$setDisabledEl)},l.select=function(e,t){angular.isDate(a.$dateValue)||(a.$dateValue=new Date(e)),!m.$mode||t?(a.$setViewValue(angular.copy(e)),a.$render(),$.autoclose&&!t&&s(function(){l.hide(!0)})):(angular.extend(h,{year:e.getFullYear(),month:e.getMonth(),date:e.getDate()}),l.setMode(m.$mode-1),l.$build())},l.setMode=function(e){m.$mode=e,D=l.$views[m.$mode],l.$build()},l.$build=function(e){e===!0&&D.built||(e!==!1||D.built)&&D.build.call(D)},l.$updateSelected=function(){for(var e=0,t=m.rows.length;t>e;e++)angular.forEach(m.rows[e],i)},l.$isSelected=function(e){return D.isSelected(e)},l.$setDisabledEl=function(e){e.disabled=D.isDisabled(e.date)},l.$selectPane=function(e){var t=D.steps,a=new Date(Date.UTC(h.year+(t.year||0)*e,h.month+(t.month||0)*e,1));angular.extend(h,{year:a.getUTCFullYear(),month:a.getUTCMonth(),date:a.getUTCDate()}),l.$build()},l.$onMouseDown=function(e){if(e.preventDefault(),e.stopPropagation(),c){var t=angular.element(e.target);"button"!==t[0].nodeName.toLowerCase()&&(t=t.parent()),t.triggerHandler("click")}},l.$onKeyDown=function(e){if(/(38|37|39|40|13)/.test(e.keyCode)&&!e.shiftKey&&!e.altKey){if(e.preventDefault(),e.stopPropagation(),13===e.keyCode)return m.$mode?m.$apply(function(){l.setMode(m.$mode-1)}):l.hide(!0);D.onKeyDown(e),g.$digest()}};var y=l.init;l.init=function(){return u&&$.useNative?(t.prop("type","date"),void t.css("-webkit-appearance","textfield")):(c&&(t.prop("type","text"),t.attr("readonly","true"),t.on("click",o)),void y())};var p=l.destroy;l.destroy=function(){u&&$.useNative&&t.off("click",o),p()};var w=l.show;l.show=function(){w(),s(function(){l.$isShown&&(l.$element.on(c?"touchstart":"mousedown",l.$onMouseDown),$.keyboard&&t.on("keydown",l.$onKeyDown))},0,!1)};var b=l.hide;return l.hide=function(e){l.$isShown&&(l.$element.off(c?"touchstart":"mousedown",l.$onMouseDown),$.keyboard&&t.off("keydown",l.$onKeyDown),b(e))},l}var u=(angular.element(t.document.body),/(ip(a|o)d|iphone|android)/gi.test(t.navigator.userAgent)),c="createTouch"in t.document&&u;return e.lang||(e.lang=o.getDefaultLocale()),l.defaults=e,l}]}).directive("bsDatepicker",["$window","$parse","$q","$dateFormatter","$dateParser","$datepicker",function(e,t,a,n,i,o){var r=(o.defaults,/(ip(a|o)d|iphone|android)/gi.test(e.navigator.userAgent));return{restrict:"EAC",require:"ngModel",link:function(e,t,a,d){function s(e){return e&&e.length?e:null}function l(e){if(angular.isDate(e)){var t=isNaN(g.$options.minDate)||e.getTime()>=g.$options.minDate,a=isNaN(g.$options.maxDate)||e.getTime()<=g.$options.maxDate,n=t&&a;d.$setValidity("date",n),d.$setValidity("min",t),d.$setValidity("max",a),n&&(d.$dateValue=e)}}function u(){return!d.$dateValue||isNaN(d.$dateValue.getTime())?"":m(d.$dateValue,c.dateFormat)}var c={scope:e,controller:d};angular.forEach(["placement","container","delay","trigger","keyboard","html","animation","template","autoclose","dateType","dateFormat","modelDateFormat","dayFormat","strictFormat","startWeek","startDate","useNative","lang","startView","minView","iconLeft","iconRight","daysOfWeekDisabled","id"],function(e){angular.isDefined(a[e])&&(c[e]=a[e])}),a.bsShow&&e.$watch(a.bsShow,function(e){g&&angular.isDefined(e)&&(angular.isString(e)&&(e=!!e.match(/true|,?(datepicker),?/i)),e===!0?g.show():g.hide())});var g=o(t,d,c);c=g.$options,r&&c.useNative&&(c.dateFormat="yyyy-MM-dd");var $=c.lang,m=function(e,t){return n.formatDate(e,t,$)},f=i({format:c.dateFormat,lang:$,strict:c.strictFormat});angular.forEach(["minDate","maxDate"],function(e){angular.isDefined(a[e])&&a.$observe(e,function(t){g.$options[e]=f.getDateForAttribute(e,t),!isNaN(g.$options[e])&&g.$build(!1),l(d.$dateValue)})}),e.$watch(a.ngModel,function(){g.update(d.$dateValue)},!0),angular.isDefined(a.disabledDates)&&e.$watch(a.disabledDates,function(e,t){e=s(e),t=s(t),e&&g.updateDisabledDates(e)}),d.$parsers.unshift(function(e){if(!e)return d.$setValidity("date",!0),null;var t=f.parse(e,d.$dateValue);return!t||isNaN(t.getTime())?void d.$setValidity("date",!1):(l(t),"string"===c.dateType?m(t,c.modelDateFormat||c.dateFormat):"number"===c.dateType?d.$dateValue.getTime():"unix"===c.dateType?d.$dateValue.getTime()/1e3:"iso"===c.dateType?d.$dateValue.toISOString():new Date(d.$dateValue))}),d.$formatters.push(function(e){var t;return t=angular.isUndefined(e)||null===e?0/0:angular.isDate(e)?e:"string"===c.dateType?f.parse(e,null,c.modelDateFormat):new Date("unix"===c.dateType?1e3*e:e),d.$dateValue=t,u()}),d.$render=function(){t.val(u())},e.$on("$destroy",function(){g&&g.destroy(),c=null,g=null})}}}]).provider("datepickerViews",function(){function e(e,t){for(var a=[];e.length>0;)a.push(e.splice(0,t));return a}function t(e,t){return(e%t+t)%t}this.defaults={dayFormat:"dd",daySplit:7};this.$get=["$dateFormatter","$dateParser","$sce",function(a,n,i){return function(o){var r=o.$scope,d=o.$options,s=d.lang,l=function(e,t){return a.formatDate(e,t,s)},u=n({format:d.dateFormat,lang:s,strict:d.strictFormat}),c=a.weekdaysShort(s),g=c.slice(d.startWeek).concat(c.slice(0,d.startWeek)),$=i.trustAsHtml('<th class="dow text-center">'+g.join('</th><th class="dow text-center">')+"</th>"),m=o.$date||(d.startDate?u.getDateForAttribute("startDate",d.startDate):new Date),f={year:m.getFullYear(),month:m.getMonth(),date:m.getDate()},h=(6e4*m.getTimezoneOffset(),[{format:d.dayFormat,split:7,steps:{month:1},update:function(e,t){!this.built||t||e.getFullYear()!==f.year||e.getMonth()!==f.month?(angular.extend(f,{year:o.$date.getFullYear(),month:o.$date.getMonth(),date:o.$date.getDate()}),o.$build()):e.getDate()!==f.date&&(f.date=o.$date.getDate(),o.$updateSelected())},build:function(){var a=new Date(f.year,f.month,1),n=a.getTimezoneOffset(),i=new Date(+a-864e5*t(a.getDay()-d.startWeek,7)),s=i.getTimezoneOffset(),c=(new Date).toDateString();s!==n&&(i=new Date(+i+6e4*(s-n)));for(var g,m=[],h=0;42>h;h++)g=u.daylightSavingAdjust(new Date(i.getFullYear(),i.getMonth(),i.getDate()+h)),m.push({date:g,isToday:g.toDateString()===c,label:l(g,this.format),selected:o.$date&&this.isSelected(g),muted:g.getMonth()!==f.month,disabled:this.isDisabled(g)});r.title=l(a,d.monthTitleFormat),r.showLabels=!0,r.labels=$,r.rows=e(m,this.split),this.built=!0},isSelected:function(e){return o.$date&&e.getFullYear()===o.$date.getFullYear()&&e.getMonth()===o.$date.getMonth()&&e.getDate()===o.$date.getDate()},isDisabled:function(e){var t=e.getTime();if(t<d.minDate||t>d.maxDate)return!0;if(-1!==d.daysOfWeekDisabled.indexOf(e.getDay()))return!0;if(d.disabledDateRanges)for(var a=0;a<d.disabledDateRanges.length;a++)if(t>=d.disabledDateRanges[a].start&&t<=d.disabledDateRanges[a].end)return!0;return!1},onKeyDown:function(e){if(o.$date){var t,a=o.$date.getTime();37===e.keyCode?t=new Date(a-864e5):38===e.keyCode?t=new Date(a-6048e5):39===e.keyCode?t=new Date(a+864e5):40===e.keyCode&&(t=new Date(a+6048e5)),this.isDisabled(t)||o.select(t,!0)}}},{name:"month",format:d.monthFormat,split:4,steps:{year:1},update:function(e){this.built&&e.getFullYear()===f.year?e.getMonth()!==f.month&&(angular.extend(f,{month:o.$date.getMonth(),date:o.$date.getDate()}),o.$updateSelected()):(angular.extend(f,{year:o.$date.getFullYear(),month:o.$date.getMonth(),date:o.$date.getDate()}),o.$build())},build:function(){for(var t,a=(new Date(f.year,0,1),[]),n=0;12>n;n++)t=new Date(f.year,n,1),a.push({date:t,label:l(t,this.format),selected:o.$isSelected(t),disabled:this.isDisabled(t)});r.title=l(t,d.yearTitleFormat),r.showLabels=!1,r.rows=e(a,this.split),this.built=!0},isSelected:function(e){return o.$date&&e.getFullYear()===o.$date.getFullYear()&&e.getMonth()===o.$date.getMonth()},isDisabled:function(e){var t=+new Date(e.getFullYear(),e.getMonth()+1,0);return t<d.minDate||e.getTime()>d.maxDate},onKeyDown:function(e){if(o.$date){var t=o.$date.getMonth(),a=new Date(o.$date);37===e.keyCode?a.setMonth(t-1):38===e.keyCode?a.setMonth(t-4):39===e.keyCode?a.setMonth(t+1):40===e.keyCode&&a.setMonth(t+4),this.isDisabled(a)||o.select(a,!0)}}},{name:"year",format:d.yearFormat,split:4,steps:{year:12},update:function(e,t){!this.built||t||parseInt(e.getFullYear()/20,10)!==parseInt(f.year/20,10)?(angular.extend(f,{year:o.$date.getFullYear(),month:o.$date.getMonth(),date:o.$date.getDate()}),o.$build()):e.getFullYear()!==f.year&&(angular.extend(f,{year:o.$date.getFullYear(),month:o.$date.getMonth(),date:o.$date.getDate()}),o.$updateSelected())},build:function(){for(var t,a=f.year-f.year%(3*this.split),n=[],i=0;12>i;i++)t=new Date(a+i,0,1),n.push({date:t,label:l(t,this.format),selected:o.$isSelected(t),disabled:this.isDisabled(t)});r.title=n[0].label+"-"+n[n.length-1].label,r.showLabels=!1,r.rows=e(n,this.split),this.built=!0},isSelected:function(e){return o.$date&&e.getFullYear()===o.$date.getFullYear()},isDisabled:function(e){var t=+new Date(e.getFullYear()+1,0,0);return t<d.minDate||e.getTime()>d.maxDate},onKeyDown:function(e){if(o.$date){var t=o.$date.getFullYear(),a=new Date(o.$date);37===e.keyCode?a.setYear(t-1):38===e.keyCode?a.setYear(t-4):39===e.keyCode?a.setYear(t+1):40===e.keyCode&&a.setYear(t+4),this.isDisabled(a)||o.select(a,!0)}}}]);return{views:d.minView?Array.prototype.slice.call(h,d.minView):h,viewDate:f}}}]});
//# sourceMappingURL=datepicker.min.js.map