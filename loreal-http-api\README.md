# LOREAL GETTING STARTED #

### Prerequisites:
* MySql server
* create 2 databases: loreal and test_loreal
* java and maven installed

### Steps to run###

* Move to project directory
* mvn package (if you don't use local mysql server, change url to database in loreal.yml and JubiqAbstractTest
* Database setup:


```
#!java

java -jar target/loreal-1.0-SNAPSHOT.jar db migrate loreal.yml 
```


* Start the server:


```
#!java

java -jar target/loreal-1.0-SNAPSHOT.jar server loreal.yml 
```


Base Endpoint: http://localhost:8090/api/

### Consume web service ###
* Need to get access token first:


```
#!java

curl --data "client_id=<EMAIL>&client_secret=123456" http://localhost:8090/api/oauth/token
```


The access token is value of field "id" in the response:


```
#!json

{"id":"13b8f794-d6b2-4bd5-ae84-ddfcd36d3e41","created":1448594314128,"grantType":"client_credentials","expiresIn":3600,"permissions":["LOREAL_GET_USER"],"userEmail":"<EMAIL>"}
```


* Get list of users:


```
#!java

curl -i -H "Accept: application/json" -H "Authorization: Bearer 13b8f794-d6b2-4bd5-ae84-ddfcd36d3e41" -X GET http://localhost:8090/api/users
```