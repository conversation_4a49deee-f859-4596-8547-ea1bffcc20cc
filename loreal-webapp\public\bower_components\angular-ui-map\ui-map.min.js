/**
 * angular-ui-map - This directive allows you to add map elements.
 * @version v0.5.0 - 2013-12-28
 * @link http://angular-ui.github.com
 * @license MIT
 */
"use strict";!function(){function a(a,b,c,d){angular.forEach(b.split(" "),function(b){window.google.maps.event.addListener(c,b,function(c){d.trigger<PERSON><PERSON><PERSON>("map-"+b,c),a.$$phase||a.$apply()})})}function b(b,d){c.directive(b,[function(){return{restrict:"A",link:function(c,e,f){c.$watch(f[b],function(b){b&&a(c,d,b,e)})}}}])}var c=angular.module("ui.map",["ui.event"]);c.value("uiMapConfig",{}).directive("uiMap",["uiMapConfig","$parse",function(b,c){var d="bounds_changed center_changed click dblclick drag dragend dragstart heading_changed idle maptypeid_changed mousemove mouseout mouseover projection_changed resize rightclick tilesloaded tilt_changed zoom_changed",e=b||{};return{restrict:"A",link:function(b,f,g){var h=angular.extend({},e,b.$eval(g.uiOptions)),i=new window.google.maps.Map(f[0],h),j=c(g.uiMap);j.assign(b,i),a(b,d,i,f)}}}]),c.value("uiMapInfoWindowConfig",{}).directive("uiMapInfoWindow",["uiMapInfoWindowConfig","$parse","$compile",function(b,c,d){var e="closeclick content_change domready position_changed zindex_changed",f=b||{};return{link:function(b,g,h){var i=angular.extend({},f,b.$eval(h.uiOptions));i.content=g[0];var j=c(h.uiMapInfoWindow),k=j(b);k||(k=new window.google.maps.InfoWindow(i),j.assign(b,k)),a(b,e,k,g),g.replaceWith("<div></div>");var l=k.open;k.open=function(a,c,e,f,h,i){d(g.contents())(b),l.call(k,a,c,e,f,h,i)}}}}]),b("uiMapMarker","animation_changed click clickable_changed cursor_changed dblclick drag dragend draggable_changed dragstart flat_changed icon_changed mousedown mouseout mouseover mouseup position_changed rightclick shadow_changed shape_changed title_changed visible_changed zindex_changed"),b("uiMapPolyline","click dblclick mousedown mousemove mouseout mouseover mouseup rightclick"),b("uiMapPolygon","click dblclick mousedown mousemove mouseout mouseover mouseup rightclick"),b("uiMapRectangle","bounds_changed click dblclick mousedown mousemove mouseout mouseover mouseup rightclick"),b("uiMapCircle","center_changed click dblclick mousedown mousemove mouseout mouseover mouseup radius_changed rightclick"),b("uiMapGroundOverlay","click dblclick")}();