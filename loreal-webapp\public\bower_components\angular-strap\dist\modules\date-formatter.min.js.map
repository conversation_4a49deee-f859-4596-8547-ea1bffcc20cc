{"version": 3, "sources": ["helpers/date-formatter.js"], "names": [], "mappings": "+EAWS,kBAAmB,UAAW,aAAA,SAAA,EAAA,yEAOjC,iBAAe,2DAOjB,MAAS,GAAA,iBAAwB,IAAA,wOA4BhC,QAAA,EAAA,GAAA", "file": "date-formatter.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.helpers.dateFormatter', [])\n\n  .service('$dateFormatter', function($locale, dateFilter) {\n\n    // The unused `lang` arguments are on purpose. The default implementation does not\n    // use them and it always uses the locale loaded into the `$locale` service.\n    // Custom implementations might use it, thus allowing different directives to\n    // have different languages.\n\n    this.getDefaultLocale = function() {\n      return $locale.id;\n    };\n\n    // Format is either a data format name, e.g. \"shortTime\" or \"fullDate\", or a date format\n    // Return either the corresponding date format or the given date format.\n    this.getDatetimeFormat = function(format, lang) {\n      return $locale.DATETIME_FORMATS[format] || format;\n    };\n\n    this.weekdaysShort = function(lang) {\n      return $locale.DATETIME_FORMATS.SHORTDAY;\n    };\n\n    function splitTimeFormat(format) {\n      return /(h+)([:\\.])?(m+)[ ]?(a?)/i.exec(format).slice(1);\n    }\n\n    // h:mm a => h\n    this.hoursFormat = function(timeFormat) {\n      return splitTimeFormat(timeFormat)[0];\n    };\n\n    // h:mm a => mm\n    this.minutesFormat = function(timeFormat) {\n      return splitTimeFormat(timeFormat)[2];\n    };\n\n    // h:mm a => :\n    this.timeSeparator = function(timeFormat) {\n      return splitTimeFormat(timeFormat)[1];\n    };\n\n    // h:mm a => true, H.mm => false\n    this.showAM = function(timeFormat) {\n      return !!splitTimeFormat(timeFormat)[3];\n    };\n\n    this.formatDate = function(date, format, lang){\n      return dateFilter(date, format);\n    };\n\n  });\n"], "sourceRoot": "/source/"}