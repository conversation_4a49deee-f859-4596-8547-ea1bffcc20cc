{"version": 3, "file": "angular-touch.min.js", "lineCount": 12, "mappings": "A;;;;;aAKC,SAAQ,CAACA,CAAD,CAASC,CAAT,CAAkBC,CAAlB,CAA6B,CAwiBtCC,QAASA,EAAkB,CAACC,CAAD,CAAgBC,CAAhB,CAA2BC,CAA3B,CAAsC,CAC/DC,CAAAC,UAAA,CAAkBJ,CAAlB,CAAiC,CAAC,QAAD,CAAW,QAAX,CAAqB,QAAQ,CAACK,CAAD,CAASC,CAAT,CAAiB,CAQ7E,MAAO,SAAQ,CAACC,CAAD,CAAQC,CAAR,CAAiBC,CAAjB,CAAuB,CAKpCC,QAASA,EAAU,CAACC,CAAD,CAAS,CAS1B,GAAKC,CAAAA,CAAL,CAAkB,MAAO,CAAA,CACzB,KAAIC,EAASC,IAAAC,IAAA,CAASJ,CAAAK,EAAT,CAAoBJ,CAAAI,EAApB,CACTC,EAAAA,EAAUN,CAAAO,EAAVD,CAAqBL,CAAAM,EAArBD,EAAsChB,CAC1C,OAAOkB,EAAP,EAvBwBC,EAuBxB,CACIP,CADJ,EAEa,CAFb,CAEII,CAFJ,EAnB0BI,EAmB1B,CAGIJ,CAHJ,EArBqBK,EAqBrB,CAIIT,CAJJ,CAIaI,CAhBa,CAJ5B,IAAIM,EAAelB,CAAA,CAAOI,CAAA,CAAKT,CAAL,CAAP,CAAnB,CAEIY,CAFJ,CAEiBO,CAFjB,CAuBIK,EAAe,CAAC,OAAD,CACd3B,EAAA4B,UAAA,CAAkBhB,CAAA,oBAAlB,CAAL,EACEe,CAAAE,KAAA,CAAkB,OAAlB,CAEFpB,EAAAqB,KAAA,CAAYnB,CAAZ,CAAqB,CACnB,MAASoB,QAAQ,CAACjB,CAAD,CAASkB,CAAT,CAAgB,CAC/BjB,CAAA,CAAcD,CACdQ,EAAA,CAAQ,CAAA,CAFuB,CADd,CAKnB,OAAUW,QAAQ,CAACD,CAAD,CAAQ,CACxBV,CAAA,CAAQ,CAAA,CADgB,CALP,CAQnB,IAAOY,QAAQ,CAACpB,CAAD,CAASkB,CAAT,CAAgB,CACzBnB,CAAA,CAAWC,CAAX,CAAJ,EACEJ,CAAAyB,OAAA,CAAa,QAAQ,EAAG,CACtBxB,CAAAyB,eAAA,CAAuB/B,CAAvB,CACAqB,EAAA,CAAahB,CAAb,CAAoB,CAAC2B,OAAQL,CAAT,CAApB,CAFsB,CAAxB,CAF2B,CARZ,CAArB,CAgBGL,CAhBH,CA5BoC,CARuC,CAA9C,CAAjC,CAD+D,CAlhBjE,IAAIrB,EAAUN,CAAAsC,OAAA,CAAe,SAAf,CAA0B,EAA1B,CAuBdhC,EAAAiC,QAAA,CAAgB,QAAhB;AAA0B,CAAC,QAAQ,EAAG,CAkBpCC,QAASA,EAAc,CAACR,CAAD,CAAQ,CAC7B,IAAIS,EAAUT,CAAAS,QAAA,EAAiBT,CAAAS,QAAAC,OAAjB,CAAwCV,CAAAS,QAAxC,CAAwD,CAACT,CAAD,CAClEW,EAAAA,CAAKX,CAAAY,eAALD,EAA6BX,CAAAY,eAAA,CAAqB,CAArB,CAA7BD,EACCX,CAAAa,cADDF,EACwBX,CAAAa,cAAAD,eADxBD,EAEIX,CAAAa,cAAAD,eAAA,CAAmC,CAAnC,CAFJD,EAGAF,CAAA,CAAQ,CAAR,CAAAI,cAHAF,EAG4BF,CAAA,CAAQ,CAAR,CAEhC,OAAO,CACLpB,EAAGsB,CAAAG,QADE,CAEL3B,EAAGwB,CAAAI,QAFE,CAPsB,CAa/BC,QAASA,EAAS,CAACrB,CAAD,CAAesB,CAAf,CAA0B,CAC1C,IAAIC,EAAM,EACVlD,EAAAmD,QAAA,CAAgBxB,CAAhB,CAA8B,QAAQ,CAACyB,CAAD,CAAc,CAElD,CADI/C,CACJ,CADgBgD,CAAA,CAAeD,CAAf,CAAA,CAA4BH,CAA5B,CAChB,GACEC,CAAArB,KAAA,CAASxB,CAAT,CAHgD,CAApD,CAMA,OAAO6C,EAAAI,KAAA,CAAS,GAAT,CARmC,CA3B5C,IAAID,EAAiB,CACnB,MAAS,CACPtB,MAAO,WADA,CAEPwB,KAAM,WAFC,CAGPrB,IAAK,SAHE,CADU,CAMnB,MAAS,CACPH,MAAO,YADA,CAEPwB,KAAM,WAFC,CAGPrB,IAAK,UAHE,CAIPD,OAAQ,aAJD,CANU,CAsCrB,OAAO,CAiCLH,KAAMA,QAAQ,CAACnB,CAAD;AAAU6C,CAAV,CAAyB7B,CAAzB,CAAuC,CAAA,IAE/C8B,CAF+C,CAEvCC,CAFuC,CAI/C3C,CAJ+C,CAM/C4C,CAN+C,CAQ/CC,EAAS,CAAA,CAEbjC,EAAA,CAAeA,CAAf,EAA+B,CAAC,OAAD,CAAU,OAAV,CAC/BhB,EAAAkD,GAAA,CAAWb,CAAA,CAAUrB,CAAV,CAAwB,OAAxB,CAAX,CAA6C,QAAQ,CAACK,CAAD,CAAQ,CAC3DjB,CAAA,CAAcyB,CAAA,CAAeR,CAAf,CACd4B,EAAA,CAAS,CAAA,CAETF,EAAA,CADAD,CACA,CADS,CAETE,EAAA,CAAU5C,CACVyC,EAAA,MAAA,EAA0BA,CAAA,MAAA,CAAuBzC,CAAvB,CAAoCiB,CAApC,CANiC,CAA7D,CAQA,KAAI8B,EAASd,CAAA,CAAUrB,CAAV,CAAwB,QAAxB,CACb,IAAImC,CAAJ,CACEnD,CAAAkD,GAAA,CAAWC,CAAX,CAAmB,QAAQ,CAAC9B,CAAD,CAAQ,CACjC4B,CAAA,CAAS,CAAA,CACTJ,EAAA,OAAA,EAA2BA,CAAA,OAAA,CAAwBxB,CAAxB,CAFM,CAAnC,CAMFrB,EAAAkD,GAAA,CAAWb,CAAA,CAAUrB,CAAV,CAAwB,MAAxB,CAAX,CAA4C,QAAQ,CAACK,CAAD,CAAQ,CAC1D,GAAK4B,CAAL,EAQK7C,CARL,CAQA,CACA,IAAID,EAAS0B,CAAA,CAAeR,CAAf,CAEbyB,EAAA,EAAUxC,IAAAC,IAAA,CAASJ,CAAAO,EAAT,CAAoBsC,CAAAtC,EAApB,CACVqC,EAAA,EAAUzC,IAAAC,IAAA,CAASJ,CAAAK,EAAT,CAAoBwC,CAAAxC,EAApB,CAEVwC,EAAA,CAAU7C,CAnHSiD,GAqHnB,CAAIN,CAAJ,EArHmBM,EAqHnB,CAAmCL,CAAnC,GAKIA,CAAJ,CAAaD,CAAb,EAEEG,CACA,CADS,CAAA,CACT,CAAAJ,CAAA,OAAA,EAA2BA,CAAA,OAAA,CAAwBxB,CAAxB,CAH7B,GAOEA,CAAAgC,eAAA,EACA,CAAAR,CAAA,KAAA,EAAyBA,CAAA,KAAA,CAAsB1C,CAAtB,CAA8BkB,CAA9B,CAR3B,CALA,CARA,CAT0D,CAA5D,CAkCArB,EAAAkD,GAAA,CAAWb,CAAA,CAAUrB,CAAV,CAAwB,KAAxB,CAAX,CAA2C,QAAQ,CAACK,CAAD,CAAQ,CACpD4B,CAAL,GACAA,CACA,CADS,CAAA,CACT,CAAAJ,CAAA,IAAA,EAAwBA,CAAA,IAAA,CAAqBhB,CAAA,CAAeR,CAAf,CAArB,CAA4CA,CAA5C,CAFxB,CADyD,CAA3D,CA7DmD,CAjChD,CA1C6B,CAAZ,CAA1B,CAuLA1B,EAAA2D,OAAA,CAAe,CAAC,UAAD,CAAa,QAAQ,CAACC,CAAD,CAAW,CAC7CA,CAAAC,UAAA,CAAmB,kBAAnB;AAAuC,CAAC,WAAD,CAAc,QAAQ,CAACC,CAAD,CAAY,CAEvEA,CAAAC,MAAA,EACA,OAAOD,EAHgE,CAAlC,CAAvC,CAD6C,CAAhC,CAAf,CAQA9D,EAAAC,UAAA,CAAkB,SAAlB,CAA6B,CAAC,QAAD,CAAW,UAAX,CAAuB,cAAvB,CACzB,QAAQ,CAACC,CAAD,CAAS8D,CAAT,CAAmBC,CAAnB,CAAiC,CA2D3CC,QAASA,EAAqB,CAACC,CAAD,CAAmBpD,CAAnB,CAAsBF,CAAtB,CAAyB,CACrD,IAAS,IAAAuD,EAAI,CAAb,CAAgBA,CAAhB,CAAoBD,CAAA/B,OAApB,CAA6CgC,CAA7C,EAAkD,CAAlD,CAAqD,CACtB,IAAA,EAAAD,CAAA,CAAiBC,CAAjB,CAAqB,CAArB,CAAA,CAA4BvD,EAAAA,CAAzD,IAzDwBwD,EAyDxB,CARK1D,IAAAC,IAAA,CAQGuD,CAAAG,CAAiBF,CAAjBE,CARH,CAQiDvD,CARjD,CAQL,EAzDwBsD,EAyDxB,CARkD1D,IAAAC,IAAA,CAAS2D,CAAT,CAAcC,CAAd,CAQlD,CAEE,MADAL,EAAAM,OAAA,CAAwBL,CAAxB,CAA2BA,CAA3B,CAA+B,CAA/B,CACO,CAAA,CAAA,CAH0C,CAMrD,MAAO,CAAA,CAP8C,CAYvDM,QAASA,EAAO,CAAChD,CAAD,CAAQ,CACtB,GAAI,EArEiBiD,IAqEjB,CAAAC,IAAAC,IAAA,EAAA,CAAaC,CAAb,CAAJ,CAAA,CAIA,IAAI3C,EAAUT,CAAAS,QAAA,EAAiBT,CAAAS,QAAAC,OAAjB,CAAwCV,CAAAS,QAAxC,CAAwD,CAACT,CAAD,CAAtE,CACIX,EAAIoB,CAAA,CAAQ,CAAR,CAAAK,QADR,CAEI3B,EAAIsB,CAAA,CAAQ,CAAR,CAAAM,QAKA,EAAR,CAAI1B,CAAJ,EAAiB,CAAjB,CAAaF,CAAb,EAGIkE,CAHJ,EAIIA,CAAA,CAA0B,CAA1B,CAJJ,GAIqChE,CAJrC,EAI0CgE,CAAA,CAA0B,CAA1B,CAJ1C,GAI2ElE,CAJ3E,GAQIkE,CAWJ,GAVEA,CAUF,CAV8B,IAU9B,EAP2C,OAO3C,GAPIrD,CAAAsD,OAAAC,QAAAC,YAAA,EAOJ,GANEH,CAMF,CAN8B,CAAChE,CAAD,CAAIF,CAAJ,CAM9B,EAAIqD,CAAA,CAAsBC,CAAtB,CAAwCpD,CAAxC,CAA2CF,CAA3C,CAAJ,GAKAa,CAAAyD,gBAAA,EAIA;AAHAzD,CAAAgC,eAAA,EAGA,CAAAhC,CAAAsD,OAAA,EAAgBtD,CAAAsD,OAAAI,KAAA,EAThB,CAnBA,CAXA,CADsB,CA8CxBC,QAASA,EAAY,CAAC3D,CAAD,CAAQ,CACvBS,CAAAA,CAAUT,CAAAS,QAAA,EAAiBT,CAAAS,QAAAC,OAAjB,CAAwCV,CAAAS,QAAxC,CAAwD,CAACT,CAAD,CACtE,KAAIX,EAAIoB,CAAA,CAAQ,CAAR,CAAAK,QAAR,CACI3B,EAAIsB,CAAA,CAAQ,CAAR,CAAAM,QACR0B,EAAA5C,KAAA,CAAsBR,CAAtB,CAAyBF,CAAzB,CAEAmD,EAAA,CAAS,QAAQ,EAAG,CAElB,IAAS,IAAAI,EAAI,CAAb,CAAgBA,CAAhB,CAAoBD,CAAA/B,OAApB,CAA6CgC,CAA7C,EAAkD,CAAlD,CACE,GAAID,CAAA,CAAiBC,CAAjB,CAAJ,EAA2BrD,CAA3B,EAAgCoD,CAAA,CAAiBC,CAAjB,CAAqB,CAArB,CAAhC,EAA2DvD,CAA3D,CAA8D,CAC5DsD,CAAAM,OAAA,CAAwBL,CAAxB,CAA2BA,CAA3B,CAA+B,CAA/B,CACA,MAF4D,CAH9C,CAApB,CAxHqBO,IAwHrB,CAQqB,CAAA,CARrB,CAN2B,CA9G7B,IAAIG,CAAJ,CACIX,CADJ,CAEIY,CA4IJ,OAAO,SAAQ,CAAC3E,CAAD,CAAQC,CAAR,CAAiBC,CAAjB,CAAuB,CAQpCgF,QAASA,EAAU,EAAG,CACpBC,CAAA,CAAU,CAAA,CACVlF,EAAAmF,YAAA,CAzJoBC,iBAyJpB,CAFoB,CARc,IAChCC,EAAexF,CAAA,CAAOI,CAAAqF,QAAP,CADiB,CAEhCJ,EAAU,CAAA,CAFsB,CAGhCK,CAHgC,CAIhCC,CAJgC,CAKhCC,CALgC,CAMhCC,CAOJ1F,EAAAkD,GAAA,CAAW,YAAX,CAAyB,QAAQ,CAAC7B,CAAD,CAAQ,CACvC6D,CAAA,CAAU,CAAA,CACVK,EAAA,CAAalE,CAAAsD,OAAA,CAAetD,CAAAsD,OAAf,CAA8BtD,CAAAsE,WAEhB,EAA3B,EAAIJ,CAAAK,SAAJ,GACEL,CADF,CACeA,CAAAM,WADf,CAIA7F,EAAA8F,SAAA,CApKoBV,iBAoKpB,CAEAI,EAAA,CAAYjB,IAAAC,IAAA,EAER1C;CAAAA,CAAUT,CAAAS,QAAA,EAAiBT,CAAAS,QAAAC,OAAjB,CAAwCV,CAAAS,QAAxC,CAAwD,CAACT,CAAD,CAClEW,EAAAA,CAAIF,CAAA,CAAQ,CAAR,CAAAI,cAAJF,EAAgCF,CAAA,CAAQ,CAAR,CACpC2D,EAAA,CAAczD,CAAAG,QACduD,EAAA,CAAc1D,CAAAI,QAfyB,CAAzC,CAkBApC,EAAAkD,GAAA,CAAW,WAAX,CAAwB,QAAQ,CAAC7B,CAAD,CAAQ,CACtC4D,CAAA,EADsC,CAAxC,CAIAjF,EAAAkD,GAAA,CAAW,aAAX,CAA0B,QAAQ,CAAC7B,CAAD,CAAQ,CACxC4D,CAAA,EADwC,CAA1C,CAIAjF,EAAAkD,GAAA,CAAW,UAAX,CAAuB,QAAQ,CAAC7B,CAAD,CAAQ,CACrC,IAAI0E,EAAOxB,IAAAC,IAAA,EAAPuB,CAAoBP,CAAxB,CAEI1D,EAAWT,CAAAY,eAAD,EAAyBZ,CAAAY,eAAAF,OAAzB,CAAwDV,CAAAY,eAAxD,CACRZ,CAAAS,QAAD,EAAkBT,CAAAS,QAAAC,OAAlB,CAA0CV,CAAAS,QAA1C,CAA0D,CAACT,CAAD,CAH/D,CAIIW,EAAIF,CAAA,CAAQ,CAAR,CAAAI,cAAJF,EAAgCF,CAAA,CAAQ,CAAR,CAJpC,CAKIpB,EAAIsB,CAAAG,QALR,CAMI3B,EAAIwB,CAAAI,QANR,CAOI4D,EAAO1F,IAAA2F,KAAA,CAAU3F,IAAA4F,IAAA,CAASxF,CAAT,CAAa+E,CAAb,CAA0B,CAA1B,CAAV,CAAyCnF,IAAA4F,IAAA,CAAS1F,CAAT,CAAakF,CAAb,CAA0B,CAA1B,CAAzC,CAEPR,EAAJ,EArMeiB,GAqMf,CAAeJ,CAAf,EApMiBK,EAoMjB,CAAsCJ,CAAtC,GA7DGlC,CAwED,GAvEFF,CAAA,CAAa,CAAb,CAAAyC,iBAAA,CAAiC,OAAjC,CAA0ChC,CAA1C,CAAmD,CAAA,CAAnD,CAEA,CADAT,CAAA,CAAa,CAAb,CAAAyC,iBAAA,CAAiC,YAAjC;AAA+CrB,CAA/C,CAA6D,CAAA,CAA7D,CACA,CAAAlB,CAAA,CAAmB,EAqEjB,EAlEJW,CAkEI,CAlEgBF,IAAAC,IAAA,EAkEhB,CAhEJX,CAAA,CAAsBC,CAAtB,CAuDsBpD,CAvDtB,CAuDyBF,CAvDzB,CAgEI,CAJI+E,CAIJ,EAHEA,CAAAR,KAAA,EAGF,CAAK1F,CAAA4B,UAAA,CAAkBhB,CAAAqG,SAAlB,CAAL,EAA2D,CAAA,CAA3D,GAAyCrG,CAAAqG,SAAzC,EACEtG,CAAAyB,eAAA,CAAuB,OAAvB,CAAgC,CAACJ,CAAD,CAAhC,CAZJ,CAgBA4D,EAAA,EA1BqC,CAAvC,CA+BAjF,EAAAuG,QAAA,CAAkBC,QAAQ,CAACnF,CAAD,CAAQ,EAQlCrB,EAAAkD,GAAA,CAAW,OAAX,CAAoB,QAAQ,CAAC7B,CAAD,CAAQoF,CAAR,CAAkB,CAC5C1G,CAAAyB,OAAA,CAAa,QAAQ,EAAG,CACtB6D,CAAA,CAAatF,CAAb,CAAoB,CAAC2B,OAAS+E,CAAT/E,EAAqBL,CAAtB,CAApB,CADsB,CAAxB,CAD4C,CAA9C,CAMArB,EAAAkD,GAAA,CAAW,WAAX,CAAwB,QAAQ,CAAC7B,CAAD,CAAQ,CACtCrB,CAAA8F,SAAA,CApOoBV,iBAoOpB,CADsC,CAAxC,CAIApF,EAAAkD,GAAA,CAAW,mBAAX,CAAgC,QAAQ,CAAC7B,CAAD,CAAQ,CAC9CrB,CAAAmF,YAAA,CAxOoBC,iBAwOpB,CAD8C,CAAhD,CAxFoC,CArJK,CADhB,CAA7B,CAuXA7F,EAAA,CAAmB,aAAnB,CAAmC,EAAnC,CAAsC,WAAtC,CACAA,EAAA,CAAmB,cAAnB,CAAmC,CAAnC,CAAsC,YAAtC,CApmBsC,CAArC,CAAD,CAwmBGH,MAxmBH,CAwmBWA,MAAAC,QAxmBX;", "sources": ["angular-touch.js"], "names": ["window", "angular", "undefined", "makeSwipeDirective", "directiveName", "direction", "eventName", "ngTouch", "directive", "$parse", "$swipe", "scope", "element", "attr", "validSwipe", "coords", "startCoords", "deltaY", "Math", "abs", "y", "deltaX", "x", "valid", "MAX_VERTICAL_DISTANCE", "MIN_HORIZONTAL_DISTANCE", "MAX_VERTICAL_RATIO", "swi<PERSON><PERSON><PERSON><PERSON>", "pointerTypes", "isDefined", "push", "bind", "start", "event", "cancel", "end", "$apply", "<PERSON><PERSON><PERSON><PERSON>", "$event", "module", "factory", "getCoordinates", "touches", "length", "e", "changedTouches", "originalEvent", "clientX", "clientY", "getEvents", "eventType", "res", "for<PERSON>ach", "pointerType", "POINTER_EVENTS", "join", "move", "eventHandlers", "totalX", "totalY", "lastPos", "active", "on", "events", "MOVE_BUFFER_RADIUS", "preventDefault", "config", "$provide", "decorator", "$delegate", "shift", "$timeout", "$rootElement", "checkAllowableRegions", "touchCoordinates", "i", "CLICKBUSTER_THRESHOLD", "x1", "y1", "y2", "splice", "onClick", "PREVENT_DURATION", "Date", "now", "lastPreventedTime", "lastLabelClickCoordinates", "target", "tagName", "toLowerCase", "stopPropagation", "blur", "onTouchStart", "resetState", "tapping", "removeClass", "ACTIVE_CLASS_NAME", "clickHandler", "ngClick", "tapElement", "startTime", "touchStartX", "touchStartY", "srcElement", "nodeType", "parentNode", "addClass", "diff", "dist", "sqrt", "pow", "TAP_DURATION", "MOVE_TOLERANCE", "addEventListener", "disabled", "onclick", "element.onclick", "touchend"]}