{"name": "angular-translate-storage-local", "description": "A plugin for Angular Translate", "version": "2.5.2", "main": "./angular-translate-storage-local.js", "dependencies": {"angular-translate": "~2.5.2", "angular-translate-storage-cookie": "~2.5.2"}, "ignore": [], "author": "<PERSON>", "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "homepage": "https://github.com/PascalPrecht/bower-angular-translate-storage-local", "_release": "2.5.2", "_resolution": {"type": "version", "tag": "2.5.2", "commit": "8b6a8e07f34cbd8ed2f9026b72aed67a16f66741"}, "_source": "git://github.com/PascalPrecht/bower-angular-translate-storage-local.git", "_target": "~2.5.2", "_originalSource": "angular-translate-storage-local", "_direct": true}