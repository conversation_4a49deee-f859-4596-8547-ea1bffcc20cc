{"name": "angular-ui-router", "version": "0.2.13", "main": "./release/angular-ui-router.js", "dependencies": {"angular": ">= 1.0.8"}, "ignore": ["**/.*", "node_modules", "bower_components", "component.json", "package.json", "lib", "config", "sample", "test", "tests", "ngdoc_assets", "Gruntfile.js", "files.js"], "homepage": "https://github.com/angular-ui/ui-router", "_release": "0.2.13", "_resolution": {"type": "version", "tag": "0.2.13", "commit": "c3d543aae43d4600512520a0d70723ac31f2cb62"}, "_source": "git://github.com/angular-ui/ui-router.git", "_target": "~0.2.11", "_originalSource": "angular-ui-router"}