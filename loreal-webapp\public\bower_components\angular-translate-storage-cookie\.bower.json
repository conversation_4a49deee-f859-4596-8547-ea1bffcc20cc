{"name": "angular-translate-storage-cookie", "description": "A plugin for Angular Translate", "version": "2.5.2", "main": "./angular-translate-storage-cookie.js", "dependencies": {"angular-translate": "~2.5.2", "angular-cookies": "~1.2.26"}, "ignore": [], "author": "<PERSON>", "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "homepage": "https://github.com/PascalPrecht/bower-angular-translate-storage-cookie", "_release": "2.5.2", "_resolution": {"type": "version", "tag": "2.5.2", "commit": "fe3332c52f089d933627e128abb856d4a48f4dcc"}, "_source": "git://github.com/PascalPrecht/bower-angular-translate-storage-cookie.git", "_target": "~2.5.2", "_originalSource": "angular-translate-storage-cookie", "_direct": true}