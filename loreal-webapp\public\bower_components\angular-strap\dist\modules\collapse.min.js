/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.collapse",[]).provider("$collapse",function(){var e=this.defaults={animation:"am-collapse",disallowToggle:!1,activeClass:"in",startCollapsed:!1,allowMultiple:!1},t=this.controller=function(t,a,n){function i(e){for(var t=o.$targets.$active,a=0;a<t.length;a++)e<t[a]&&(t[a]=t[a]-1),t[a]===o.$targets.length&&(t[a]=o.$targets.length-1)}function s(e){var t=o.$targets.$active;return-1===t.indexOf(e)?!1:!0}function r(e){var t=o.$targets.$active.indexOf(e);-1!==t&&o.$targets.$active.splice(t,1)}function l(e){o.$options.allowMultiple||o.$targets.$active.splice(0,1),-1===o.$targets.$active.indexOf(e)&&o.$targets.$active.push(e)}var o=this;o.$options=angular.copy(e),angular.forEach(["animation","disallowToggle","activeClass","startCollapsed","allowMultiple"],function(e){angular.isDefined(n[e])&&(o.$options[e]=n[e])}),o.$toggles=[],o.$targets=[],o.$viewChangeListeners=[],o.$registerToggle=function(e){o.$toggles.push(e)},o.$registerTarget=function(e){o.$targets.push(e)},o.$unregisterToggle=function(e){var t=o.$toggles.indexOf(e);o.$toggles.splice(t,1)},o.$unregisterTarget=function(e){var t=o.$targets.indexOf(e);o.$targets.splice(t,1),o.$options.allowMultiple&&r(e),i(t),o.$viewChangeListeners.forEach(function(e){e()})},o.$targets.$active=o.$options.startCollapsed?[]:[0],o.$setActive=t.$setActive=function(e){angular.isArray(e)?o.$targets.$active=angular.copy(e):o.$options.disallowToggle?l(e):s(e)?r(e):l(e),o.$viewChangeListeners.forEach(function(e){e()})},o.$activeIndexes=function(){return o.$options.allowMultiple?o.$targets.$active:1===o.$targets.$active.length?o.$targets.$active[0]:-1}};this.$get=function(){var a={};return a.defaults=e,a.controller=t,a}}).directive("bsCollapse",["$window","$animate","$collapse",function(e,t,a){a.defaults;return{require:["?ngModel","bsCollapse"],controller:["$scope","$element","$attrs",a.controller],link:function(e,t,a,n){var i=n[0],s=n[1];i&&(s.$viewChangeListeners.push(function(){i.$setViewValue(s.$activeIndexes())}),i.$formatters.push(function(e){if(angular.isArray(e))s.$setActive(e);else{var t=s.$activeIndexes();angular.isArray(t)?-1===t.indexOf(1*e)&&s.$setActive(1*e):t!==1*e&&s.$setActive(1*e)}return e}))}}}]).directive("bsCollapseToggle",function(){return{require:["^?ngModel","^bsCollapse"],link:function(e,t,a,n){var i=(n[0],n[1]);t.attr("data-toggle","collapse"),i.$registerToggle(t),e.$on("$destroy",function(){i.$unregisterToggle(t)}),t.on("click",function(){var n=a.bsCollapseToggle||i.$toggles.indexOf(t);i.$setActive(1*n),e.$apply()})}}}).directive("bsCollapseTarget",["$animate",function(e){return{require:["^?ngModel","^bsCollapse"],link:function(t,a,n,i){function s(){var t=r.$targets.indexOf(a),n=r.$activeIndexes(),i="removeClass";angular.isArray(n)?-1!==n.indexOf(t)&&(i="addClass"):t===n&&(i="addClass"),e[i](a,r.$options.activeClass)}var r=(i[0],i[1]);a.addClass("collapse"),r.$options.animation&&a.addClass(r.$options.animation),r.$registerTarget(a),t.$on("$destroy",function(){r.$unregisterTarget(a)}),r.$viewChangeListeners.push(function(){s()}),s()}}}]);
//# sourceMappingURL=collapse.min.js.map