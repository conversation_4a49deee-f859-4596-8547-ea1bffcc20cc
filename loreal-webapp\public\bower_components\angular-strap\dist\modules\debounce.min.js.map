{"version": 3, "sources": ["helpers/debounce.js"], "names": [], "mappings": "qBASI,OAAO,8CAIF,YAAS,WAAA,SAAA,SACV,UAAS,EAAO,EAAA,oBAElB,eACE,GAAU,KACV,EAAI,YACG,IAAe,cAEf,OAAA,KAEF,EAAM,kBAEb,sDAeG,YAAU,WAAA,SAAA,SACX,UAAG,EAAQ,EAAA,SACJ,sBAEP,cACE,GAAU,OACP,gCAGF,MAAM,EAAA,yBAId,EAAA,YAAA", "file": "debounce.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.helpers.debounce', [])\n\n// @source jashkenas/underscore\n// @url https://github.com/jashkenas/underscore/blob/1.5.2/underscore.js#L693\n.factory('debounce', function($timeout) {\n  return function(func, wait, immediate) {\n    var timeout = null;\n    return function() {\n      var context = this,\n        args = arguments,\n        callNow = immediate && !timeout;\n      if(timeout) {\n        $timeout.cancel(timeout);\n      }\n      timeout = $timeout(function later() {\n        timeout = null;\n        if(!immediate) {\n          func.apply(context, args);\n        }\n      }, wait, false);\n      if(callNow) {\n        func.apply(context, args);\n      }\n      return timeout;\n    };\n  };\n})\n\n\n// @source jashkenas/underscore\n// @url https://github.com/jashkenas/underscore/blob/1.5.2/underscore.js#L661\n.factory('throttle', function($timeout) {\n  return function(func, wait, options) {\n    var timeout = null;\n    options || (options = {});\n    return function() {\n      var context = this,\n        args = arguments;\n      if(!timeout) {\n        if(options.leading !== false) {\n          func.apply(context, args);\n        }\n        timeout = $timeout(function later() {\n          timeout = null;\n          if(options.trailing !== false) {\n            func.apply(context, args);\n          }\n        }, wait, false);\n      }\n    };\n  };\n});\n\n"], "sourceRoot": "/source/"}