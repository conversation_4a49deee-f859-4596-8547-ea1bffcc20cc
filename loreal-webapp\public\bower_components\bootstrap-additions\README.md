# [BootstrapAdditions](http://mgcrea.github.io/bootstrap-additions) [![Build Status](https://secure.travis-ci.org/mgcrea/bootstrap-additions.png?branch=master)](http://travis-ci.org/#!/mgcrea/bootstrap-additions) [![Dependency Status](https://gemnasium.com/mgcrea/bootstrap-additions.png)](https://gemnasium.com/mgcrea/bootstrap-additions)

[![Banner](http://mgcrea.github.io/bootstrap-additions/images/snippet.png)](http://mgcrea.github.io/bootstrap-additions)

BootstrapAdditions is a CSS extension kit for [TwitterBootstrap 3.0+](https://github.com/twbs/bootstrap).

It's a spin off from [AngularStrap](http://mgcrea.github.io/angular-strap) v2 release work.


## Documentation and examples

+ Check the [documentation](http://mgcrea.github.io/bootstrap-additions) and [changelog](https://github.com/mgcrea/bootstrap-additions/releases).


## Quick start

+ Include the required libraries (cdn/local)

>
``` html
<link rel="stylesheet" href="//rawgithub.com/mgcrea/bootstrap-additions/master/dist/bootstrap-additions.min.css">
```

## Developers

Clone the repo, `git clone git://github.com/mgcrea/bootstrap-additions.git`, [download the latest release](https://github.com/mgcrea/bootstrap-additions/zipball/master) or install with bower `bower install bootstrap-additions --save`.

BootstrapAdditions is tested with `karma` against the latest stable release of AngularJS.

>
    $ npm install grunt-cli --global
    $ npm install --dev
    $ grunt test

You can build the latest version using `grunt`.

>
    $ grunt build

You can quickly hack around (the docs) with:

>
    $ grunt serve



## Contributing

Please submit all pull requests the against master branch. If your unit test contains JavaScript patches or features, you should include relevant unit tests. Thanks!



## Authors

**Olivier Louvignes**

+ http://olouv.com
+ http://github.com/mgcrea



## Copyright and license

    The MIT License

    Copyright (c) 2014 Olivier Louvignes

    Permission is hereby granted, free of charge, to any person obtaining a copy
    of this software and associated documentation files (the "Software"), to deal
    in the Software without restriction, including without limitation the rights
    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
    copies of the Software, and to permit persons to whom the Software is
    furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in
    all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
    THE SOFTWARE.
