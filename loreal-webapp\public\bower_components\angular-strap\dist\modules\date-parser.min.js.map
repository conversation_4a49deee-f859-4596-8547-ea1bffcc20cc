{"version": 3, "sources": ["helpers/date-parser.js"], "names": [], "mappings": "qBASI,OAAK,iDAEA,eAAM,kBAAA,mBAIN,iCAGP,KAAA,IAAU,EACV,KAAA,MAAU,EACV,KAAA,QAAU,EACV,KAAA,QAAU,EACV,KAAA,aAAoB,EA4BpB,QAAS,cAGD,GAAS,4DAMf,IAAA,GADE,GAAA,EAAW,OAAK,EAAA,EAAW,WAAA,cACrB,EAAA,EAAA,EAAA,EAAA,IACR,GAAA,EAAQ,GAAA,gBAAA,EAAA,MAAA,YAnCV,EAAU,UAAU,gBAAc,SAAS,GAAS,KAAK,aAAO,GAChE,EAAU,UAAU,WAAW,SAAS,GAAO,KAAA,QAAA,KACxC,UAAa,WAAA,SAAA,GAAA,KAAA,QAAA,KACb,UAAQ,SAAM,SAAA,GAAA,KAAA,MAAA,KACd,UAAY,SAAA,WAAA,MAAA,MAAA,SACZ,UAAQ,QAAM,SAAA,GAAA,KAAA,IAAA,KACd,UAAU,SAAM,SAAA,GAAA,KAAA,MAAA,KAChB,UAAU,YAAM,SAAA,GAAA,KAAA,KAAA,KAChB,UAAA,SAAqB,SAAA,GAQ5B,MAPE,MAAA,KAAO,EAAA,2DAGT,KAAA,MAAU,EAAU,WAClB,KAAA,QAAW,EAAU,+EAGnB,6JAwBF,EAAI,KAAA,qCAEE,sDAKF,GAAU,SAAA,WAmJN,GAAY,MACe,GAA3B,EAAA,OAAS,KAAO,GAChB,KAAG,+EAMP,GAAQ,EAAQ,MAAK,EAAS,IAAG,KAAA,4BAYnC,oDAAS,UAGH,GAAK,2IAOP,EAAK,CAEP,KAAA,EAAA,EAAS,EAAA,EAAA,OAAA,4DAKX,EAAA,EAAY,MAAA,KAAA,EAAA,KAAA,KAAA,IAAA,EAAA,EAAA,IAAA,uDAtLV,EAAU,QAAQ,UAAS,EAAA,GAE3B,KAEA,GACA,IAAU,WACV,GAAU,aACV,EAAU,EAAA,OAAA,cAAA,mBACV,GAAU,aACV,EAAU,EAAQ,OAAA,cAAiB,mBACnC,GAAU,mBACV,EAAU,EAAQ,OAAS,iBAAA,oBAC3B,GAAU,oBACV,EAAU,EAAQ,OAAA,eAAiB,iBACnC,EAAU,QACV,KAAU,EAAQ,iBAAS,IAAA,KAAiB,KAC5C,IAAU,EAAA,iBAAA,SAAA,KAAA,KACV,GAAU,yBACV,EAAU,EAAQ,OAAS,yBAA0B,+GAGvD,GAAI,gBACF,EAAU,EAAM,OAAA,eAAA,iBAChB,KAAU,gCACV,GAAU,WACV,EAAU,EAAM,OAAA,wBAAA,kBAGhB,GACA,IAAU,EAAM,gBAChB,GAAU,EAAM,WAChB,EAAU,EAAA,WACV,GAAU,EAAA,WACV,EAAU,EAAM,WAChB,GAAU,EAAM,SAChB,EAAU,EAAA,SACV,GAAU,EAAA,SACV,EAAU,EAAA,SACV,KAAU,EACV,IAAU,EACV,GAAU,EAAM,QAChB,EAAU,EAAA,QACV,EAAU,SAAM,GAAA,GAAA,GAAA,KAAA,WAAA,EAAA,OAAA,MAAA,SAAA,EAAA,MAAA,OAAA,EAAA,GAAA,oJAGlB,GAAW,SAAA,GAAA,MAAA,MAAA,SAAA,EAAA,EAAA,+CAEX,KAAY,EAAA,YACV,GAAA,SAAY,GAAU,MAAQ,MAAA,YAAiB,IAAQ,EAAA,IACvD,EAAQ,EAAA,sBAKL,KAAQ,WACX,EAAO,QAAW,EAAA,iBAAA,EAAA,SAAA,EAAA,wCAKf,QAAiB,SAAQ,GAC5B,MAAG,SAAQ,OAAO,IAAQ,MAAQ,EAAA,WAC9B,EAAA,KAAc,MAGd,MAAS,SAAO,EAAA,EAAA,GAEpB,IAAW,EAAA,EAAa,iBAAe,IAAiB,GACxD,QAAQ,OAAO,KAAI,EAAQ,EAAiB,EAAA,GAAA,EAAA,aAC1C,GAAa,EAAM,EAAqB,GAAM,wBAGhD,KAAI,EAAA,OAAe,CAGnB,KAAI,sEAAA,EAAA,EAAS,EAAK,EAAK,OAAQ,EAAQ,IACrC,EAAO,IAAA,EAAA,GAAA,KAAA,EAAA,EAAA,EAAA,GAGT,IAAA,GAAO,EAAA,QAGT,OAAA,UAAY,EAAA,IAAA,MAAA,EAAsB,WAC5B,EAGE,KAGG,oBAAsB,SAAS,EAAA,YAG7B,YAAQ,CACjB,GAAA,GAAO,GAAQ,QACV,GAAA,MAAA,EAAA,cAAA,EAAA,WAAA,EAAA,WAAA,YAAA,EAAA,EAAA,GAAA,EAAA,EAAA,EAAA,YAAA,EAAA,EAAA,WACE,QAAS,SAAA,IAAA,EAAA,MAAA,gDAGX,GAAA,MAAA,SAAA,EAAA,+DAIH,GAAA,MAAA,SAGF,MAGQ,oBAAkB,SAAA,EAAA,MAC1B,YAEO,QAAP,GACK,GAAA,OAAA,YAAA,KAAA,EAAA,GACE,QAAA,SAAkB,IAAO,EAAS,MAAM,sEAG1C,GAAA,MAAA,SAAA,EAAA,KAAA,YAAA,KAAA,EAAA,2IAuBP,EAAI,SAAO,EAAO,WAAK,GAAW,EAAA,WAAA,EAAA,GAC9B", "file": "date-parser.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.helpers.dateParser', [])\n\n.provider('$dateParser', function($localeProvider) {\n\n  // define a custom ParseDate object to use instead of native Date\n  // to avoid date values wrapping when setting date component values\n  function ParseDate() {\n    this.year = 1970;\n    this.month = 0;\n    this.day = 1;\n    this.hours = 0;\n    this.minutes = 0;\n    this.seconds = 0;\n    this.milliseconds = 0;\n  }\n\n  ParseDate.prototype.setMilliseconds = function(value) { this.milliseconds = value; };\n  ParseDate.prototype.setSeconds = function(value) { this.seconds = value; };\n  ParseDate.prototype.setMinutes = function(value) { this.minutes = value; };\n  ParseDate.prototype.setHours = function(value) { this.hours = value; };\n  ParseDate.prototype.getHours = function() { return this.hours; };\n  ParseDate.prototype.setDate = function(value) { this.day = value; };\n  ParseDate.prototype.setMonth = function(value) { this.month = value; };\n  ParseDate.prototype.setFullYear = function(value) { this.year = value; };\n  ParseDate.prototype.fromDate = function(value) {\n    this.year = value.getFullYear();\n    this.month = value.getMonth();\n    this.day = value.getDate();\n    this.hours = value.getHours();\n    this.minutes = value.getMinutes();\n    this.seconds = value.getSeconds();\n    this.milliseconds = value.getMilliseconds();\n    return this;\n  };\n\n  ParseDate.prototype.toDate = function() {\n    return new Date(this.year, this.month, this.day, this.hours, this.minutes, this.seconds, this.milliseconds);\n  };\n\n  var proto = ParseDate.prototype;\n\n  function noop() {\n  }\n\n  function isNumeric(n) {\n    return !isNaN(parseFloat(n)) && isFinite(n);\n  }\n\n  function indexOfCaseInsensitive(array, value) {\n    var len = array.length, str=value.toString().toLowerCase();\n    for (var i=0; i<len; i++) {\n      if (array[i].toLowerCase() === str) { return i; }\n    }\n    return -1; // Return -1 per the \"Array.indexOf()\" method.\n  }\n\n  var defaults = this.defaults = {\n    format: 'shortDate',\n    strict: false\n  };\n\n  this.$get = function($locale, dateFilter) {\n\n    var DateParserFactory = function(config) {\n\n      var options = angular.extend({}, defaults, config);\n\n      var $dateParser = {};\n\n      var regExpMap = {\n        'sss'   : '[0-9]{3}',\n        'ss'    : '[0-5][0-9]',\n        's'     : options.strict ? '[1-5]?[0-9]' : '[0-9]|[0-5][0-9]',\n        'mm'    : '[0-5][0-9]',\n        'm'     : options.strict ? '[1-5]?[0-9]' : '[0-9]|[0-5][0-9]',\n        'HH'    : '[01][0-9]|2[0-3]',\n        'H'     : options.strict ? '1?[0-9]|2[0-3]' : '[01]?[0-9]|2[0-3]',\n        'hh'    : '[0][1-9]|[1][012]',\n        'h'     : options.strict ? '[1-9]|1[012]' : '0?[1-9]|1[012]',\n        'a'     : 'AM|PM',\n        'EEEE'  : $locale.DATETIME_FORMATS.DAY.join('|'),\n        'EEE'   : $locale.DATETIME_FORMATS.SHORTDAY.join('|'),\n        'dd'    : '0[1-9]|[12][0-9]|3[01]',\n        'd'     : options.strict ? '[1-9]|[1-2][0-9]|3[01]' : '0?[1-9]|[1-2][0-9]|3[01]',\n        'MMMM'  : $locale.DATETIME_FORMATS.MONTH.join('|'),\n        'MMM'   : $locale.DATETIME_FORMATS.SHORTMONTH.join('|'),\n        'MM'    : '0[1-9]|1[012]',\n        'M'     : options.strict ? '[1-9]|1[012]' : '0?[1-9]|1[012]',\n        'yyyy'  : '[1]{1}[0-9]{3}|[2]{1}[0-9]{3}',\n        'yy'    : '[0-9]{2}',\n        'y'     : options.strict ? '-?(0|[1-9][0-9]{0,3})' : '-?0*[0-9]{1,4}',\n      };\n\n      var setFnMap = {\n        'sss'   : proto.setMilliseconds,\n        'ss'    : proto.setSeconds,\n        's'     : proto.setSeconds,\n        'mm'    : proto.setMinutes,\n        'm'     : proto.setMinutes,\n        'HH'    : proto.setHours,\n        'H'     : proto.setHours,\n        'hh'    : proto.setHours,\n        'h'     : proto.setHours,\n        'EEEE'  : noop,\n        'EEE'   : noop,\n        'dd'    : proto.setDate,\n        'd'     : proto.setDate,\n        'a'     : function(value) { var hours = this.getHours() % 12; return this.setHours(value.match(/pm/i) ? hours + 12 : hours); },\n        'MMMM'  : function(value) { return this.setMonth(indexOfCaseInsensitive($locale.DATETIME_FORMATS.MONTH, value)); },\n        'MMM'   : function(value) { return this.setMonth(indexOfCaseInsensitive($locale.DATETIME_FORMATS.SHORTMONTH, value)); },\n        'MM'    : function(value) { return this.setMonth(1 * value - 1); },\n        'M'     : function(value) { return this.setMonth(1 * value - 1); },\n        'yyyy'  : proto.setFullYear,\n        'yy'    : function(value) { return this.setFullYear(2000 + 1 * value); },\n        'y'     : proto.setFullYear\n      };\n\n      var regex, setMap;\n\n      $dateParser.init = function() {\n        $dateParser.$format = $locale.DATETIME_FORMATS[options.format] || options.format;\n        regex = regExpForFormat($dateParser.$format);\n        setMap = setMapForFormat($dateParser.$format);\n      };\n\n      $dateParser.isValid = function(date) {\n        if(angular.isDate(date)) return !isNaN(date.getTime());\n        return regex.test(date);\n      };\n\n      $dateParser.parse = function(value, baseDate, format) {\n        // check for date format special names\n        if(format) format = $locale.DATETIME_FORMATS[format] || format;\n        if(angular.isDate(value)) value = dateFilter(value, format || $dateParser.$format);\n        var formatRegex = format ? regExpForFormat(format) : regex;\n        var formatSetMap = format ? setMapForFormat(format) : setMap;\n        var matches = formatRegex.exec(value);\n        if(!matches) return false;\n        // use custom ParseDate object to set parsed values\n        var date = baseDate && !isNaN(baseDate.getTime()) ? new ParseDate().fromDate(baseDate) : new ParseDate().fromDate(new Date(1970, 0, 1, 0));\n        for(var i = 0; i < matches.length - 1; i++) {\n          formatSetMap[i] && formatSetMap[i].call(date, matches[i+1]);\n        }\n        // convert back to native Date object\n        var newDate = date.toDate();\n\n        // check new native Date object for day values overflow\n        if (parseInt(date.day, 10) !== newDate.getDate()) {\n          return false;\n        }\n\n        return newDate;\n      };\n\n      $dateParser.getDateForAttribute = function(key, value) {\n        var date;\n\n        if(value === 'today') {\n          var today = new Date();\n          date = new Date(today.getFullYear(), today.getMonth(), today.getDate() + (key === 'maxDate' ? 1 : 0), 0, 0, 0, (key === 'minDate' ? 0 : -1));\n        } else if(angular.isString(value) && value.match(/^\".+\"$/)) { // Support {{ dateObj }}\n          date = new Date(value.substr(1, value.length - 2));\n        } else if(isNumeric(value)) {\n          date = new Date(parseInt(value, 10));\n        } else if (angular.isString(value) && 0 === value.length) { // Reset date\n          date = key === 'minDate' ? -Infinity : +Infinity;\n        } else {\n          date = new Date(value);\n        }\n\n        return date;\n      };\n\n      $dateParser.getTimeForAttribute = function(key, value) {\n        var time;\n\n        if(value === 'now') {\n          time = new Date().setFullYear(1970, 0, 1);\n        } else if(angular.isString(value) && value.match(/^\".+\"$/)) {\n          time = new Date(value.substr(1, value.length - 2)).setFullYear(1970, 0, 1);\n        } else if(isNumeric(value)) {\n          time = new Date(parseInt(value, 10)).setFullYear(1970, 0, 1);\n        } else if (angular.isString(value) && 0 === value.length) { // Reset time\n          time = key === 'minTime' ? -Infinity : +Infinity;\n        } else {\n          time = $dateParser.parse(value, new Date(1970, 0, 1, 0));\n        }\n\n        return time;\n      };\n\n      /* Handle switch to/from daylight saving.\n      * Hours may be non-zero on daylight saving cut-over:\n      * > 12 when midnight changeover, but then cannot generate\n      * midnight datetime, so jump to 1AM, otherwise reset.\n      * @param  date  (Date) the date to check\n      * @return  (Date) the corrected date\n      *\n      * __ copied from jquery ui datepicker __\n      */\n      $dateParser.daylightSavingAdjust = function(date) {\n        if (!date) {\n          return null;\n        }\n        date.setHours(date.getHours() > 12 ? date.getHours() + 2 : 0);\n        return date;\n      };\n\n      // Private functions\n\n      function setMapForFormat(format) {\n        var keys = Object.keys(setFnMap), i;\n        var map = [], sortedMap = [];\n        // Map to setFn\n        var clonedFormat = format;\n        for(i = 0; i < keys.length; i++) {\n          if(format.split(keys[i]).length > 1) {\n            var index = clonedFormat.search(keys[i]);\n            format = format.split(keys[i]).join('');\n            if(setFnMap[keys[i]]) {\n              map[index] = setFnMap[keys[i]];\n            }\n          }\n        }\n        // Sort result map\n        angular.forEach(map, function(v) {\n          // conditional required since angular.forEach broke around v1.2.21\n          // related pr: https://github.com/angular/angular.js/pull/8525\n          if(v) sortedMap.push(v);\n        });\n        return sortedMap;\n      }\n\n      function escapeReservedSymbols(text) {\n        return text.replace(/\\//g, '[\\\\/]').replace('/-/g', '[-]').replace(/\\./g, '[.]').replace(/\\\\s/g, '[\\\\s]');\n      }\n\n      function regExpForFormat(format) {\n        var keys = Object.keys(regExpMap), i;\n\n        var re = format;\n        // Abstract replaces to avoid collisions\n        for(i = 0; i < keys.length; i++) {\n          re = re.split(keys[i]).join('${' + i + '}');\n        }\n        // Replace abstracted values\n        for(i = 0; i < keys.length; i++) {\n          re = re.split('${' + i + '}').join('(' + regExpMap[keys[i]] + ')');\n        }\n        format = escapeReservedSymbols(format);\n\n        return new RegExp('^' + re + '$', ['i']);\n      }\n\n      $dateParser.init();\n      return $dateParser;\n\n    };\n\n    return DateParserFactory;\n\n  };\n\n});\n"], "sourceRoot": "/source/"}