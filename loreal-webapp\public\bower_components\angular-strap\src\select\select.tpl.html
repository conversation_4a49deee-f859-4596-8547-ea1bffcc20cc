<ul tabindex="-1" class="select dropdown-menu" ng-show="$isVisible()" role="select">
  <li ng-if="$showAllNoneButtons">
    <div class="btn-group" style="margin-bottom: 5px; margin-left: 5px">
      <button type="button" class="btn btn-default btn-xs" ng-click="$selectAll()">{{$allText}}</button>
      <button type="button" class="btn btn-default btn-xs" ng-click="$selectNone()">{{$noneText}}</button>
    </div>
  </li>
  <li role="presentation" ng-repeat="match in $matches" ng-class="{active: $isActive($index)}">
    <a style="cursor: default;" role="menuitem" tabindex="-1" ng-click="$select($index, $event)">
      <i class="{{$iconCheckmark}} pull-right" ng-if="$isMultiple && $isActive($index)"></i>
      <span ng-bind="match.label"></span>
    </a>
  </li>
</ul>
