## 5.5 Directory Listing Enabled

### Impact
- Attackers can download backend files, view source code, or read configuration files that may contain hardcoded credentials, API keys, or DB connection strings.
- Makes it easier for attackers to fingerprint the backend technologies (e.g., Java, Spring Boot, database types, libraries used) and craft targeted exploits.
- Exposed .log files may reveal stack traces, SQL queries, session tokens, or internal server errors.
- Files like .bak, .old, .zip, or .tar may include sensitive or outdated data which should not be publicly accessible.

### Description
The web server is configured to allow directory listing, meaning that when users access certain URLs that point to a folder (rather than a specific file), the server returns an auto-generated list of all files and subdirectories inside that folder.

During testing, we discovered that directory listing is enabled on one or more paths. The exposed content included:
- Source code files
- Configuration files
- Database-related files
- Log and backup files
- Compiled Java .jar files and other technology indicators

This kind of exposure gives attackers a shortcut to understand the internal structure of your application and technologies used.

### Recommendation
- Disable Directory Listing.
- Block public access to .sql, .log, .env, .jar, and other sensitive extensions using server rules.
- Remove or relocate backup, temporary, and debug files from public web directories.
- Set up alerts for access to unusual file types or directory paths.
- Perform regular black-box scans to detect accidental exposure of server directories.

---

## 5.6 No SSL Certificate

### Impact
- Attackers on the same network (e.g., public Wi-Fi, internal LAN, or via compromised routers) can capture login credentials using tools like Wireshark or tcpdump.
- Without HTTPS, session cookies can also be intercepted and reused by attackers to hijack user sessions.
- The lack of encryption enables attackers to modify traffic in transit (inject scripts, change content, redirect users).
- This setup violates security best practices and may fail audits for standards like OWASP Top 10, ISO 27001, or GDPR.

### Description
The application transmits user credentials (username and password) over unencrypted HTTP (port 80) instead of secure HTTPS. During testing, it was observed that:
- Login requests are sent over http:// instead of https://
- There is no SSL/TLS certificate configured on the server
- Sensitive data like credentials are visible in plain text during transmission
- Port 80 is open and actively in use for serving traffic, with no redirection to HTTPS

This means all data between the user and the server—including login credentials—are sent in unencrypted form, which can be intercepted and read by any attacker monitoring the network.

### Recommendation
- Install a valid SSL/TLS certificate.
- Redirect all HTTP (port 80) traffic to HTTPS (port 443).
- Ensure all forms, login pages, and API requests send data over HTTPS only.
- Make sure base URLs and endpoints in frontend code and backend configs use https://.
- Use the HTTP Strict Transport Security (HSTS) header to force browsers to use HTTPS for all future visits.
- Once HTTPS redirection is confirmed, you may restrict or close port 80 to reduce attack surface.

---

## 5.7 Improper Error Handling

### Impact
- A verbose error message might reveal a SQL query with the user's input, exposing how the application constructs its queries. This information can be valuable for constructing SQL injection attacks.  
- An error message might disclose the exact version of a web application framework, like Apache Struts. Attackers can then search for known vulnerabilities in that specific version.  
- Revealing the full path of a file could allow an attacker to locate and potentially access sensitive configuration file.

### Description
An Improper Error Handling vulnerability occurs when an application, during an error state, reveals too much information about its internal workings, potentially exposing sensitive data or system details to attackers. This can include things like file paths, database connection strings, server configurations, stack traces, or even the specific versions of software being used. Attackers can leverage this information to launch targeted attacks, identify vulnerabilities, and gain unauthorized access.

### Recommendation
- Implement proper error handling in the application to prevent sensitive information from being included in error messages.
- Use custom error messages.

---

