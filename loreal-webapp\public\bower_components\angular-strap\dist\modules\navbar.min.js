/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.navbar",[]).provider("$navbar",function(){var t=this.defaults={activeClass:"active",routeAttr:"data-match-route",strict:!1};this.$get=function(){return{defaults:t}}}).directive("bsNavbar",["$window","$location","$navbar",function(t,a,r){var e=r.defaults;return{restrict:"A",link:function(t,r,n){var i=angular.copy(e);angular.forEach(Object.keys(e),function(t){angular.isDefined(n[t])&&(i[t]=n[t])}),t.$watch(function(){return a.path()},function(t){var a=r[0].querySelectorAll("li["+i.routeAttr+"]");angular.forEach(a,function(a){var r=angular.element(a),e=r.attr(i.routeAttr).replace("/","\\/");i.strict&&(e="^"+e+"$");var n=new RegExp(e,["i"]);n.test(t)?r.addClass(i.activeClass):r.removeClass(i.activeClass)})})}}}]);
//# sourceMappingURL=navbar.min.js.map