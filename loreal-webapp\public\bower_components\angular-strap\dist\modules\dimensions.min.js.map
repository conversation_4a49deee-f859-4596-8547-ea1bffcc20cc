{"version": 3, "sources": ["helpers/dimensions.js"], "names": [], "mappings": "8HAcQ,wHAiBA,SAAQ,EAAc,EAAA,kBAExB,EAAO,oIAgBC,SAAQ,2TAiBhB,SAAW,SAAS,MAGlB,8FAeA,EAAA,EAAA,OAAiB,gCAMjB,EAAgB,KAAA,EAAA,IAAA,EAAA,kBAAA,GAChB,EAAY,MAAM,EAAA,IAAA,EAA8B,mBAAsB,wIAkBxE,GAAO,SAA2B,uMAiBlC,OAAO,SAAA,EAAA,iNAiBP,MAAO,SAAA,EAAA,gCAGT,GAAO,EAAA,IAAA,EAAA,cAAA,GAAA,EAAA,IAAA,EAAA,eAAA,GAEN,GAAA,EAAA,IAAA,EAAA,eAAA,GAAA,EAAA,IAAA,EAAA,gBAAA,GAAA,EAAA,IAAA,EAAA,mBAAA,GAAA,EAAA,IAAA,EAAA,oBAAA", "file": "dimensions.min.js", "sourcesContent": ["'use strict';\n\nangular.module('mgcrea.ngStrap.helpers.dimensions', [])\n\n  .factory('dimensions', function($document, $window) {\n\n    var jqLite = angular.element;\n    var fn = {};\n\n    /**\n     * Test the element nodeName\n     * @param element\n     * @param name\n     */\n    var nodeName = fn.nodeName = function(element, name) {\n      return element.nodeName && element.nodeName.toLowerCase() === name.toLowerCase();\n    };\n\n    /**\n     * Returns the element computed style\n     * @param element\n     * @param prop\n     * @param extra\n     */\n    fn.css = function(element, prop, extra) {\n      var value;\n      if (element.currentStyle) { //IE\n        value = element.currentStyle[prop];\n      } else if (window.getComputedStyle) {\n        value = window.getComputedStyle(element)[prop];\n      } else {\n        value = element.style[prop];\n      }\n      return extra === true ? parseFloat(value) || 0 : value;\n    };\n\n    /**\n     * Provides read-only equivalent of jQuery's offset function:\n     * @required-by bootstrap-tooltip, bootstrap-affix\n     * @url http://api.jquery.com/offset/\n     * @param element\n     */\n    fn.offset = function(element) {\n      var boxRect = element.getBoundingClientRect();\n      var docElement = element.ownerDocument;\n      return {\n        width: boxRect.width || element.offsetWidth,\n        height: boxRect.height || element.offsetHeight,\n        top: boxRect.top + (window.pageYOffset || docElement.documentElement.scrollTop) - (docElement.documentElement.clientTop || 0),\n        left: boxRect.left + (window.pageXOffset || docElement.documentElement.scrollLeft) - (docElement.documentElement.clientLeft || 0)\n      };\n    };\n\n    /**\n     * Provides read-only equivalent of jQuery's position function\n     * @required-by bootstrap-tooltip, bootstrap-affix\n     * @url http://api.jquery.com/offset/\n     * @param element\n     */\n    fn.position = function(element) {\n\n      var offsetParentRect = {top: 0, left: 0},\n          offsetParentElement,\n          offset;\n\n      // Fixed elements are offset from window (parentOffset = {top:0, left: 0}, because it is it's only offset parent\n      if (fn.css(element, 'position') === 'fixed') {\n\n        // We assume that getBoundingClientRect is available when computed position is fixed\n        offset = element.getBoundingClientRect();\n\n      } else {\n\n        // Get *real* offsetParentElement\n        offsetParentElement = offsetParent(element);\n\n        // Get correct offsets\n        offset = fn.offset(element);\n        if (!nodeName(offsetParentElement, 'html')) {\n          offsetParentRect = fn.offset(offsetParentElement);\n        }\n\n        // Add offsetParent borders\n        offsetParentRect.top += fn.css(offsetParentElement, 'borderTopWidth', true);\n        offsetParentRect.left += fn.css(offsetParentElement, 'borderLeftWidth', true);\n      }\n\n      // Subtract parent offsets and element margins\n      return {\n        width: element.offsetWidth,\n        height: element.offsetHeight,\n        top: offset.top - offsetParentRect.top - fn.css(element, 'marginTop', true),\n        left: offset.left - offsetParentRect.left - fn.css(element, 'marginLeft', true)\n      };\n\n    };\n\n    /**\n     * Returns the closest, non-statically positioned offsetParent of a given element\n     * @required-by fn.position\n     * @param element\n     */\n    var offsetParent = function offsetParentElement(element) {\n      var docElement = element.ownerDocument;\n      var offsetParent = element.offsetParent || docElement;\n      if(nodeName(offsetParent, '#document')) return docElement.documentElement;\n      while(offsetParent && !nodeName(offsetParent, 'html') && fn.css(offsetParent, 'position') === 'static') {\n        offsetParent = offsetParent.offsetParent;\n      }\n      return offsetParent || docElement.documentElement;\n    };\n\n    /**\n     * Provides equivalent of jQuery's height function\n     * @required-by bootstrap-affix\n     * @url http://api.jquery.com/height/\n     * @param element\n     * @param outer\n     */\n    fn.height = function(element, outer) {\n      var value = element.offsetHeight;\n      if(outer) {\n        value += fn.css(element, 'marginTop', true) + fn.css(element, 'marginBottom', true);\n      } else {\n        value -= fn.css(element, 'paddingTop', true) + fn.css(element, 'paddingBottom', true) + fn.css(element, 'borderTopWidth', true) + fn.css(element, 'borderBottomWidth', true);\n      }\n      return value;\n    };\n\n    /**\n     * Provides equivalent of jQuery's width function\n     * @required-by bootstrap-affix\n     * @url http://api.jquery.com/width/\n     * @param element\n     * @param outer\n     */\n    fn.width = function(element, outer) {\n      var value = element.offsetWidth;\n      if(outer) {\n        value += fn.css(element, 'marginLeft', true) + fn.css(element, 'marginRight', true);\n      } else {\n        value -= fn.css(element, 'paddingLeft', true) + fn.css(element, 'paddingRight', true) + fn.css(element, 'borderLeftWidth', true) + fn.css(element, 'borderRightWidth', true);\n      }\n      return value;\n    };\n\n    return fn;\n\n  });\n"], "sourceRoot": "/source/"}