/**
 * angular-strap
 * @version v2.1.6 - 2015-01-11
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
"use strict";angular.module("mgcrea.ngStrap.button",[]).provider("$button",function(){var e=this.defaults={activeClass:"active",toggleEvent:"click"};this.$get=function(){return{defaults:e}}}).directive("bsCheckboxGroup",function(){return{restrict:"A",require:"ngModel",compile:function(e,t){e.attr("data-toggle","buttons"),e.removeAttr("ng-model");var a=e[0].querySelectorAll('input[type="checkbox"]');angular.forEach(a,function(e){var a=angular.element(e);a.attr("bs-checkbox",""),a.attr("ng-model",t.ngModel+"."+a.attr("value"))})}}}).directive("bsCheckbox",["$button","$$rAF",function(e,t){var a=e.defaults,n=/^(true|false|\d+)$/;return{restrict:"A",require:"ngModel",link:function(e,r,u,l){var o=a,i="INPUT"===r[0].nodeName,c=i?r.parent():r,s=angular.isDefined(u.trueValue)?u.trueValue:!0;n.test(u.trueValue)&&(s=e.$eval(u.trueValue));var d=angular.isDefined(u.falseValue)?u.falseValue:!1;n.test(u.falseValue)&&(d=e.$eval(u.falseValue));var f="boolean"!=typeof s||"boolean"!=typeof d;f&&(l.$parsers.push(function(e){return e?s:d}),l.$formatters.push(function(e){return angular.equals(e,s)}),e.$watch(u.ngModel,function(){l.$render()})),l.$render=function(){var e=angular.equals(l.$modelValue,s);t(function(){i&&(r[0].checked=e),c.toggleClass(o.activeClass,e)})},r.bind(o.toggleEvent,function(){e.$apply(function(){i||l.$setViewValue(!c.hasClass("active")),f||l.$render()})})}}}]).directive("bsRadioGroup",function(){return{restrict:"A",require:"ngModel",compile:function(e,t){e.attr("data-toggle","buttons"),e.removeAttr("ng-model");var a=e[0].querySelectorAll('input[type="radio"]');angular.forEach(a,function(e){angular.element(e).attr("bs-radio",""),angular.element(e).attr("ng-model",t.ngModel)})}}}).directive("bsRadio",["$button","$$rAF",function(e,t){var a=e.defaults,n=/^(true|false|\d+)$/;return{restrict:"A",require:"ngModel",link:function(e,r,u,l){var o=a,i="INPUT"===r[0].nodeName,c=i?r.parent():r,s=n.test(u.value)?e.$eval(u.value):u.value;l.$render=function(){var e=angular.equals(l.$modelValue,s);t(function(){i&&(r[0].checked=e),c.toggleClass(o.activeClass,e)})},r.bind(o.toggleEvent,function(){e.$apply(function(){l.$setViewValue(s),l.$render()})})}}}]);
//# sourceMappingURL=button.min.js.map